/**
 * Centralized error logging utility for the Go42 project
 * Provides consistent error handling and reporting across all components
 */

export type ErrorSeverity = 'critical' | 'warning' | 'info'

export interface ErrorContext {
  componentStack?: string
  errorBoundary?: string
  errorBoundaryStack?: string
  userId?: string
  sessionId?: string
  timestamp?: string
  url?: string
  userAgent?: string
}

export interface LogErrorOptions {
  severity?: ErrorSeverity
  context?: ErrorContext
  tags?: string[]
  extra?: Record<string, any>
}

/**
 * Centralized error logging function
 * 
 * @param message - Descriptive message about where the error occurred
 * @param error - The actual error object
 * @param options - Additional logging options
 */
export const logError = (
  message: string, 
  error: Error, 
  options: LogErrorOptions = {}
): void => {
  const {
    severity = 'warning',
    context = {},
    tags = [],
    extra = {}
  } = options

  // Enhanced error information
  const errorInfo = {
    message,
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
    },
    severity,
    context: {
      ...context,
      timestamp: new Date().toISOString(),
      url: typeof window !== 'undefined' ? window.location.href : undefined,
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : undefined,
    },
    tags,
    extra,
  }

  // Console logging with appropriate level
  const logLevel = severity === 'critical' ? 'error' : severity === 'warning' ? 'warn' : 'info'
  console[logLevel](`[${severity.toUpperCase()}] ${message}:`, errorInfo)

  // In production, send to error tracking service
  if (process.env.NODE_ENV === 'production') {
    // TODO: Integrate with error tracking service (Sentry, LogRocket, etc.)
    // Example for Sentry:
    // Sentry.captureException(error, {
    //   level: severity,
    //   tags,
    //   extra: { ...extra, context },
    //   contexts: {
    //     errorBoundary: context
    //   }
    // })
    
    // For now, we'll use a simple fetch to a logging endpoint
    // This should be replaced with your actual error tracking service
    try {
      if (typeof window !== 'undefined' && window.fetch) {
        fetch('/api/errors', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(errorInfo),
        }).catch(() => {
          // Silently fail if error reporting fails
          // to avoid infinite error loops
        })
      }
    } catch {
      // Silently fail if error reporting fails
    }
  }
}

/**
 * Specialized error logger for React Error Boundaries
 * 
 * @param componentName - Name of the component where error occurred
 * @param error - The error object
 * @param errorInfo - React error boundary error info
 */
export const logErrorBoundaryError = (
  componentName: string,
  error: Error,
  errorInfo: { componentStack: string }
): void => {
  logError(`${componentName} error boundary caught an error`, error, {
    severity: 'critical',
    context: {
      errorBoundary: componentName,
      componentStack: errorInfo.componentStack,
    },
    tags: ['error-boundary', componentName.toLowerCase()],
  })
}

/**
 * Specialized error logger for non-critical component errors
 * 
 * @param componentName - Name of the component where error occurred
 * @param error - The error object
 * @param description - Additional description of the error context
 */
export const logComponentError = (
  componentName: string,
  error: Error,
  description?: string
): void => {
  logError(`${componentName} component error${description ? `: ${description}` : ''}`, error, {
    severity: 'warning',
    tags: ['component-error', componentName.toLowerCase()],
    extra: {
      description,
    },
  })
}

/**
 * Specialized error logger for async operations
 * 
 * @param operation - Name of the async operation
 * @param error - The error object
 * @param context - Additional context about the operation
 */
export const logAsyncError = (
  operation: string,
  error: Error,
  context?: Record<string, any>
): void => {
  logError(`Async operation failed: ${operation}`, error, {
    severity: 'warning',
    tags: ['async-error', operation.toLowerCase()],
    extra: context,
  })
}

/**
 * Specialized error logger for API errors
 * 
 * @param endpoint - API endpoint that failed
 * @param error - The error object
 * @param requestData - Data that was sent with the request
 */
export const logApiError = (
  endpoint: string,
  error: Error,
  requestData?: any
): void => {
  logError(`API request failed: ${endpoint}`, error, {
    severity: 'warning',
    tags: ['api-error'],
    extra: {
      endpoint,
      requestData,
    },
  })
}
