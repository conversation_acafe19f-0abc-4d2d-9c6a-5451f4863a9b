/**
 * Unit tests for the error logging utility
 * Tests all logging functions and error handling scenarios
 */

import {
  logError,
  logErrorBoundaryError,
  logComponentError,
  logAsyncError,
  logApiError,
  type ErrorSeverity,
  type LogErrorOptions,
} from '../error-logger'

// Mock console methods
const mockConsoleError = jest.fn()
const mockConsoleWarn = jest.fn()
const mockConsoleInfo = jest.fn()

// Mock fetch for production error reporting
const mockFetch = jest.fn()

// Mock window object
const mockWindow = {
  location: { href: 'https://test.example.com/page' },
  navigator: { userAgent: 'Test User Agent' },
  fetch: mockFetch,
}

describe('Error Logger Utility', () => {
  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks()
    
    // Mock console methods
    global.console = {
      ...console,
      error: mockConsoleError,
      warn: mockConsoleWarn,
      info: mockConsoleInfo,
    }

    // Mock window object
    Object.defineProperty(global, 'window', {
      value: mockWindow,
      writable: true,
    })

    // Mock process.env
    process.env.NODE_ENV = 'test'
  })

  afterEach(() => {
    // Restore original process.env
    delete process.env.NODE_ENV
  })

  describe('logError', () => {
    const testError = new Error('Test error message')
    const testMessage = 'Test context message'

    it('should log error with default severity (warning)', () => {
      logError(testMessage, testError)

      expect(mockConsoleWarn).toHaveBeenCalledWith(
        '[WARNING] Test context message:',
        expect.objectContaining({
          message: testMessage,
          error: {
            name: 'Error',
            message: 'Test error message',
            stack: expect.any(String),
          },
          severity: 'warning',
          context: expect.objectContaining({
            timestamp: expect.any(String),
            url: 'https://test.example.com/page',
            userAgent: 'Test User Agent',
          }),
          tags: [],
          extra: {},
        })
      )
    })

    it('should log error with critical severity', () => {
      const options: LogErrorOptions = { severity: 'critical' }
      logError(testMessage, testError, options)

      expect(mockConsoleError).toHaveBeenCalledWith(
        '[CRITICAL] Test context message:',
        expect.objectContaining({
          severity: 'critical',
        })
      )
    })

    it('should log error with info severity', () => {
      const options: LogErrorOptions = { severity: 'info' }
      logError(testMessage, testError, options)

      expect(mockConsoleInfo).toHaveBeenCalledWith(
        '[INFO] Test context message:',
        expect.objectContaining({
          severity: 'info',
        })
      )
    })

    it('should include custom context, tags, and extra data', () => {
      const options: LogErrorOptions = {
        severity: 'warning',
        context: {
          componentStack: 'Component stack trace',
          userId: 'user123',
        },
        tags: ['test-tag', 'error-tag'],
        extra: {
          customData: 'test value',
          requestId: 'req-123',
        },
      }

      logError(testMessage, testError, options)

      expect(mockConsoleWarn).toHaveBeenCalledWith(
        '[WARNING] Test context message:',
        expect.objectContaining({
          context: expect.objectContaining({
            componentStack: 'Component stack trace',
            userId: 'user123',
            timestamp: expect.any(String),
            url: 'https://test.example.com/page',
            userAgent: 'Test User Agent',
          }),
          tags: ['test-tag', 'error-tag'],
          extra: {
            customData: 'test value',
            requestId: 'req-123',
          },
        })
      )
    })

    it('should handle server-side rendering (no window)', () => {
      // Remove window object
      delete (global as any).window

      logError(testMessage, testError)

      expect(mockConsoleWarn).toHaveBeenCalledWith(
        '[WARNING] Test context message:',
        expect.objectContaining({
          context: expect.objectContaining({
            url: undefined,
            userAgent: undefined,
          }),
        })
      )
    })

    it('should attempt to send error to tracking service in production', () => {
      process.env.NODE_ENV = 'production'

      // Mock console to verify production behavior
      const consoleSpy = jest.spyOn(console, 'warn')

      logError(testMessage, testError)

      // Should still log to console in production
      expect(consoleSpy).toHaveBeenCalledWith(
        '[WARNING] Test context message:',
        expect.objectContaining({
          message: testMessage,
          severity: 'warning',
        })
      )

      consoleSpy.mockRestore()
    })

    it('should handle fetch errors silently in production', () => {
      process.env.NODE_ENV = 'production'
      mockFetch.mockRejectedValue(new Error('Network error'))

      // Should not throw
      expect(() => logError(testMessage, testError)).not.toThrow()
    })

    it('should handle missing fetch in production', () => {
      process.env.NODE_ENV = 'production'
      delete (global as any).window.fetch

      // Should not throw
      expect(() => logError(testMessage, testError)).not.toThrow()
    })
  })

  describe('logErrorBoundaryError', () => {
    it('should log error boundary errors with critical severity', () => {
      const error = new Error('Component crashed')
      const errorInfo = { componentStack: 'Component stack trace' }

      logErrorBoundaryError('TestComponent', error, errorInfo)

      expect(mockConsoleError).toHaveBeenCalledWith(
        '[CRITICAL] TestComponent error boundary caught an error:',
        expect.objectContaining({
          severity: 'critical',
          context: expect.objectContaining({
            errorBoundary: 'TestComponent',
            componentStack: 'Component stack trace',
          }),
          tags: ['error-boundary', 'testcomponent'],
        })
      )
    })
  })

  describe('logComponentError', () => {
    it('should log component errors with warning severity', () => {
      const error = new Error('Component error')

      logComponentError('MyComponent', error, 'Failed to render')

      expect(mockConsoleWarn).toHaveBeenCalledWith(
        '[WARNING] MyComponent component error: Failed to render:',
        expect.objectContaining({
          severity: 'warning',
          tags: ['component-error', 'mycomponent'],
          extra: {
            description: 'Failed to render',
          },
        })
      )
    })

    it('should log component errors without description', () => {
      const error = new Error('Component error')

      logComponentError('MyComponent', error)

      expect(mockConsoleWarn).toHaveBeenCalledWith(
        '[WARNING] MyComponent component error:',
        expect.objectContaining({
          tags: ['component-error', 'mycomponent'],
          extra: {
            description: undefined,
          },
        })
      )
    })
  })

  describe('logAsyncError', () => {
    it('should log async operation errors', () => {
      const error = new Error('Async operation failed')
      const context = { operationId: 'op-123', timeout: 5000 }

      logAsyncError('fetchUserData', error, context)

      expect(mockConsoleWarn).toHaveBeenCalledWith(
        '[WARNING] Async operation failed: fetchUserData:',
        expect.objectContaining({
          severity: 'warning',
          tags: ['async-error', 'fetchuserdata'],
          extra: context,
        })
      )
    })

    it('should log async errors without context', () => {
      const error = new Error('Async operation failed')

      logAsyncError('fetchUserData', error)

      expect(mockConsoleWarn).toHaveBeenCalledWith(
        '[WARNING] Async operation failed: fetchUserData:',
        expect.objectContaining({
          tags: ['async-error', 'fetchuserdata'],
          extra: {},
        })
      )
    })
  })

  describe('logApiError', () => {
    it('should log API errors with endpoint and request data', () => {
      const error = new Error('API request failed')
      const requestData = { userId: 123, action: 'update' }

      logApiError('/api/users/123', error, requestData)

      expect(mockConsoleWarn).toHaveBeenCalledWith(
        '[WARNING] API request failed: /api/users/123:',
        expect.objectContaining({
          severity: 'warning',
          tags: ['api-error'],
          extra: {
            endpoint: '/api/users/123',
            requestData,
          },
        })
      )
    })

    it('should log API errors without request data', () => {
      const error = new Error('API request failed')

      logApiError('/api/users/123', error)

      expect(mockConsoleWarn).toHaveBeenCalledWith(
        '[WARNING] API request failed: /api/users/123:',
        expect.objectContaining({
          extra: {
            endpoint: '/api/users/123',
            requestData: undefined,
          },
        })
      )
    })
  })

  describe('Error object handling', () => {
    it('should handle errors with custom properties', () => {
      const customError = new Error('Custom error')
      ;(customError as any).code = 'CUSTOM_ERROR_CODE'
      ;(customError as any).statusCode = 500

      logError('Custom error test', customError)

      expect(mockConsoleWarn).toHaveBeenCalledWith(
        '[WARNING] Custom error test:',
        expect.objectContaining({
          error: {
            name: 'Error',
            message: 'Custom error',
            stack: expect.any(String),
          },
        })
      )
    })

    it('should handle errors without stack traces', () => {
      const errorWithoutStack = new Error('Error without stack')
      delete errorWithoutStack.stack

      logError('Stack-less error test', errorWithoutStack)

      expect(mockConsoleWarn).toHaveBeenCalledWith(
        '[WARNING] Stack-less error test:',
        expect.objectContaining({
          error: {
            name: 'Error',
            message: 'Error without stack',
            stack: undefined,
          },
        })
      )
    })
  })
})
