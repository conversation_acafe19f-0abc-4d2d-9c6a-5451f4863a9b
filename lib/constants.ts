/**
 * Application constants for the Go42 project
 * Extracted magic numbers and configuration values for better maintainability
 */

// Spotlight animation constants
export const SPOTLIGHT_CONFIG = {
  /** Default radius of the spotlight effect */
  DEFAULT_RADIUS: 120,
  /** Inner radius for spotlight fade effect */
  INNER_RADIUS: 60,
  /** Outer radius for spotlight fade effect */
  OUTER_RADIUS: 140,
  /** Spotlight element size (width and height in pixels) */
  SIZE: 60, // w-60 h-60 in Tailwind = 240px, but this refers to the CSS class number
} as const

// Typing animation constants
export const TYPING_ANIMATION = {
  /** Delay between each character being typed (milliseconds) */
  CHARACTER_DELAY: 22,
  /** Delay between completing one line and starting the next (milliseconds) */
  LINE_DELAY: 420,
  /** Delay at the end of the animation cycle before restarting (milliseconds) */
  CYCLE_END_DELAY: 3000,
} as const

// Layout calculation constants
export const LAYOUT_CONFIG = {
  /** Cell height for problem element placement grid */
  CELL_HEIGHT: 120,
  /** Minimum number of columns in the grid layout */
  MIN_COLS: 6,
  /** Minimum number of rows in the grid layout */
  MIN_ROWS: 4,
  /** Divisor for calculating cell width from container width */
  CELL_WIDTH_DIVISOR: 220,
} as const

// CSS class size mappings (for reference)
export const TAILWIND_SIZES = {
  /** Tailwind w-60 and h-60 class size in pixels */
  SIZE_60: 240, // 15rem = 240px
} as const
