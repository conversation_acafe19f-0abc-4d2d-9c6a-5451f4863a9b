#!/usr/bin/env node

/**
 * Test Setup Validation Script
 * Validates that all test files are properly configured and can be discovered
 */

const fs = require('fs')
const path = require('path')

console.log('🧪 Validating Test Setup...\n')

// Check for required test files
const testFiles = [
  'lib/__tests__/error-logger.test.ts',
  'components/ui/__tests__/TypingTerminal.test.tsx',
  'hooks/__tests__/spotlight-constants.test.ts'
]

// Check for configuration files
const configFiles = [
  'jest.config.js',
  'jest.setup.js',
  'package.json'
]

let allValid = true

// Validate test files exist
console.log('📁 Checking test files...')
testFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`  ✅ ${file}`)
  } else {
    console.log(`  ❌ ${file} - Missing!`)
    allValid = false
  }
})

// Validate configuration files
console.log('\n⚙️  Checking configuration files...')
configFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`  ✅ ${file}`)
  } else {
    console.log(`  ❌ ${file} - Missing!`)
    allValid = false
  }
})

// Check package.json for test scripts
console.log('\n📦 Checking package.json test scripts...')
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
  const requiredScripts = ['test', 'test:watch', 'test:coverage', 'test:ci']
  
  requiredScripts.forEach(script => {
    if (packageJson.scripts && packageJson.scripts[script]) {
      console.log(`  ✅ ${script}: ${packageJson.scripts[script]}`)
    } else {
      console.log(`  ❌ ${script} - Missing!`)
      allValid = false
    }
  })

  // Check for testing dependencies
  console.log('\n📚 Checking testing dependencies...')
  const requiredDeps = [
    '@testing-library/jest-dom',
    '@testing-library/react',
    '@testing-library/user-event',
    'jest',
    'jest-environment-jsdom',
    'ts-jest'
  ]

  requiredDeps.forEach(dep => {
    if (packageJson.devDependencies && packageJson.devDependencies[dep]) {
      console.log(`  ✅ ${dep}: ${packageJson.devDependencies[dep]}`)
    } else {
      console.log(`  ❌ ${dep} - Missing!`)
      allValid = false
    }
  })

} catch (error) {
  console.log('  ❌ Error reading package.json:', error.message)
  allValid = false
}

// Check Jest configuration
console.log('\n🃏 Checking Jest configuration...')
try {
  const jestConfig = require(path.resolve('jest.config.js'))
  
  if (jestConfig) {
    console.log('  ✅ Jest configuration loaded successfully')
    
    // Check for key configuration options
    const configChecks = [
      { key: 'testEnvironment', expected: 'jsdom' },
      { key: 'setupFilesAfterEnv', expected: ['<rootDir>/jest.setup.js'] }
    ]
    
    // Note: Since jest.config.js exports a function, we can't easily check the final config
    // This would require actually running the createJestConfig function
    console.log('  ℹ️  Configuration details require runtime evaluation')
  }
} catch (error) {
  console.log('  ❌ Error loading Jest configuration:', error.message)
  allValid = false
}

// Summary
console.log('\n' + '='.repeat(50))
if (allValid) {
  console.log('🎉 All test setup validation checks passed!')
  console.log('\n📋 Next steps:')
  console.log('  1. Install dependencies: npm install')
  console.log('  2. Run tests: npm run test')
  console.log('  3. Check coverage: npm run test:coverage')
  console.log('  4. Set up CI/CD: npm run test:ci')
} else {
  console.log('❌ Some validation checks failed!')
  console.log('\n🔧 Please fix the missing files/configurations above.')
  process.exit(1)
}

console.log('\n📖 For more information, see: docs/dev/testing-guide.md')
