"use client"

import React, { useEffect, useState, useMemo } from "react"
import { TYPING_ANIMATION } from "@/lib/constants"
import { LoadingSpinner } from "@/components/ui/loading-spinner"

/**
 * Custom hook to detect user's reduced motion preference
 * @returns boolean indicating if user prefers reduced motion
 */
function useReducedMotion(): boolean {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false)

  useEffect(() => {
    // Check if we're in a browser environment
    if (typeof window === 'undefined') return

    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    setPrefersReducedMotion(mediaQuery.matches)

    const handleChange = (event: MediaQueryListEvent) => {
      setPrefersReducedMotion(event.matches)
    }

    mediaQuery.addEventListener('change', handleChange)
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [])

  return prefersReducedMotion
}

interface TypingTerminalProps {
  /** Array of lines to type out in sequence */
  lines: string[]
  /** Additional CSS classes for the container */
  className?: string
  /** Index of the line that should be bold (optional) */
  boldLineIndex?: number
  /** Text to show after the bold line (optional) */
  boldLineIndicator?: string
  /** Loading text to show during initialization */
  loadingText?: string
}

/**
 * TypingTerminal Component
 *
 * A reusable terminal-like component that displays text with a typing animation effect.
 * Features looping animation, customizable styling, and loading states.
 *
 * @param props - Component props
 * @returns JSX.Element - The rendered typing terminal
 */
export function TypingTerminal({
  lines,
  className = "",
  boldLineIndex,
  boldLineIndicator = ">>>",
  loadingText = "Initializing diagnostic agent...",
}: TypingTerminalProps) {
  const [typedLines, setTypedLines] = useState<string[]>(() => new Array(lines.length).fill(""))
  const [activeLine, setActiveLine] = useState<number>(0)
  const [isTypingInitialized, setIsTypingInitialized] = useState(false)

  // Check if user prefers reduced motion
  const prefersReducedMotion = useReducedMotion()

  // Memoized empty array for resetting typed lines
  const emptyLines = useMemo(() => new Array(lines.length).fill(""), [lines.length])

  // Terminal-like typing effect with looping and end pause + caret blink
  useEffect(() => {
    let cancelled = false
    const delay = (ms: number) => new Promise(r => setTimeout(r, ms))

    ;(async () => {
      // Set initialized state after a brief delay
      setTimeout(() => setIsTypingInitialized(true), 100)

      // If user prefers reduced motion, show all text immediately
      if (prefersReducedMotion) {
        setTypedLines(lines)
        setActiveLine(-1) // No active line for reduced motion
        return
      }

      while (!cancelled) {
        // reset for a new cycle
        setTypedLines([...emptyLines])
        for (let i = 0; i < lines.length; i++) {
          if (cancelled) return
          setActiveLine(i)
          const text = lines[i]
          if (!text) continue // Skip if text is undefined
          for (let j = 0; j <= text.length; j++) {
            if (cancelled) return
            setTypedLines(prev => {
              const next = [...prev] // Use spread operator for better performance
              next[i] = text.slice(0, j)
              return next
            })
            await delay(TYPING_ANIMATION.CHARACTER_DELAY)
          }
          await delay(TYPING_ANIMATION.LINE_DELAY)
        }
        // After finishing the last line, keep caret blinking on the last line for a few seconds
        setActiveLine(lines.length - 1)
        await delay(TYPING_ANIMATION.CYCLE_END_DELAY)
        setActiveLine(-1)
      }
    })()

    return () => {
      cancelled = true
    }
  }, [lines, emptyLines, prefersReducedMotion])

  return (
    <div className={`overflow-hidden ${className}`}>
      {/* Screen reader support for typing animation */}
      <div
        role="status"
        aria-live="polite"
        className="sr-only"
        aria-label="Terminal output"
      >
        {isTypingInitialized ? (
          prefersReducedMotion ? (
            // For reduced motion, announce all lines at once
            `Terminal output: ${lines.join('. ')}`
          ) : (
            // For normal animation, announce current line being typed
            activeLine >= 0 && activeLine < lines.length ?
              `Typing: ${typedLines[activeLine] || ''}` :
              loadingText
          )
        ) : (
          loadingText
        )}
      </div>

      {/* Rounded rectangle, auto height, gradient background */}
      <div
        className="relative rounded-2xl px-6 py-6 bg-gradient-to-br from-cyan-50 to-emerald-50"
        aria-hidden="true"
      >
        <div className="space-y-3">
          {!isTypingInitialized ? (
            <div className="flex items-center space-x-2 text-slate-700 font-mono pl-[1ch]">
              <LoadingSpinner size="sm" color="secondary" />
              <span>{loadingText}</span>
            </div>
          ) : (
            lines.map((line, idx) => (
              <div key={idx} className="text-slate-700 font-mono pl-[1ch]">
                {idx === boldLineIndex ? (
                  <span className="font-bold">{typedLines[idx]}</span>
                ) : (
                  <span>{typedLines[idx]}</span>
                )}
                {idx === boldLineIndex && typedLines[boldLineIndex] === line ? (
                  <span className="ml-2 font-bold">{boldLineIndicator}</span>
                ) : null}
                {activeLine === idx && !prefersReducedMotion ? (
                  <span className="typing-caret" aria-hidden="true" />
                ) : null}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  )
}
