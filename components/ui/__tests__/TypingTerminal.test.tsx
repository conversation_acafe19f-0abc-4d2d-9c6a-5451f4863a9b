/**
 * Accessibility tests for the TypingTerminal component
 * Tests reduced motion support, screen reader compatibility, and ARIA attributes
 */

import React from 'react'
import { render, screen, waitFor, act } from '@testing-library/react'
import { TypingTerminal } from '../TypingTerminal'

// Mock the constants
jest.mock('@/lib/constants', () => ({
  TYPING_ANIMATION: {
    CHARACTER_DELAY: 10, // Faster for testing
    LINE_DELAY: 20,
    CYCLE_END_DELAY: 50,
  },
  LAYOUT_CONFIG: {
    CELL_HEIGHT: 120,
    MIN_COLS: 6,
    MIN_ROWS: 4,
    CELL_WIDTH_DIVISOR: 220,
  },
}))

// Mock LoadingSpinner component
jest.mock('../loading-spinner', () => ({
  LoadingSpinner: ({ size, color }: { size: string; color: string }) => (
    <div data-testid="loading-spinner" data-size={size} data-color={color}>
      Loading...
    </div>
  ),
}))

describe('TypingTerminal Accessibility', () => {
  const testLines = [
    'Analyzing network traffic...',
    'Detecting anomalies...',
    'Generating report...',
  ]

  beforeEach(() => {
    // Reset any existing media query mocks
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: jest.fn().mockImplementation((query) => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: jest.fn(), // deprecated
        removeListener: jest.fn(), // deprecated
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      })),
    })
  })

  describe('Screen Reader Support', () => {
    it('should have proper ARIA attributes for screen readers', async () => {
      render(<TypingTerminal lines={testLines} />)

      // Check for screen reader status region
      const statusRegion = screen.getByRole('status')
      expect(statusRegion).toBeInTheDocument()
      expect(statusRegion).toHaveAttribute('aria-live', 'polite')
      expect(statusRegion).toHaveAttribute('aria-label', 'Terminal output')
      expect(statusRegion).toHaveClass('sr-only')

      // Check that visual terminal is hidden from screen readers
      const terminalContainer = screen.getByRole('status').nextElementSibling
      expect(terminalContainer).toHaveAttribute('aria-hidden', 'true')
    })

    it('should announce loading text initially', () => {
      const customLoadingText = 'Custom loading message'
      render(<TypingTerminal lines={testLines} loadingText={customLoadingText} />)

      const statusRegion = screen.getByRole('status')
      expect(statusRegion).toHaveTextContent(customLoadingText)
    })

    it('should announce typing progress for screen readers', async () => {
      render(<TypingTerminal lines={testLines} />)

      const statusRegion = screen.getByRole('status')

      // Wait for initialization
      await waitFor(() => {
        expect(statusRegion).not.toHaveTextContent('Initializing diagnostic agent...')
      })

      // Should announce typing progress
      await waitFor(() => {
        expect(statusRegion.textContent).toMatch(/Typing:/)
      }, { timeout: 1000 })
    })

    it('should have proper structure for assistive technology', () => {
      render(<TypingTerminal lines={testLines} />)

      // Check that there's exactly one status region
      const statusRegions = screen.getAllByRole('status')
      expect(statusRegions).toHaveLength(1)

      // Check that the visual content is properly hidden
      const hiddenContent = screen.getByLabelText('Terminal output').nextElementSibling
      expect(hiddenContent).toHaveAttribute('aria-hidden', 'true')
    })
  })

  describe('Reduced Motion Support', () => {
    it('should respect prefers-reduced-motion setting', async () => {
      // Mock reduced motion preference
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: jest.fn().mockImplementation((query) => ({
          matches: query === '(prefers-reduced-motion: reduce)',
          media: query,
          onchange: null,
          addListener: jest.fn(),
          removeListener: jest.fn(),
          addEventListener: jest.fn(),
          removeEventListener: jest.fn(),
          dispatchEvent: jest.fn(),
        })),
      })

      render(<TypingTerminal lines={testLines} />)

      const statusRegion = screen.getByRole('status')

      // Wait for initialization
      await waitFor(() => {
        expect(statusRegion).not.toHaveTextContent('Initializing diagnostic agent...')
      })

      // Should show all content immediately for reduced motion
      await waitFor(() => {
        expect(statusRegion.textContent).toMatch(/Terminal output: Analyzing network traffic\.\.\.\. Detecting anomalies\.\.\.\. Generating report\.\.\./)
      })

      // Should not show typing caret with reduced motion
      const carets = document.querySelectorAll('.typing-caret')
      expect(carets).toHaveLength(0)
    })

    it('should show typing animation when reduced motion is not preferred', async () => {
      // Mock normal motion preference
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: jest.fn().mockImplementation((query) => ({
          matches: false,
          media: query,
          onchange: null,
          addListener: jest.fn(),
          removeListener: jest.fn(),
          addEventListener: jest.fn(),
          removeEventListener: jest.fn(),
          dispatchEvent: jest.fn(),
        })),
      })

      render(<TypingTerminal lines={testLines} />)

      // Wait for initialization and typing to start
      await waitFor(() => {
        const statusRegion = screen.getByRole('status')
        expect(statusRegion.textContent).toMatch(/Typing:/)
      }, { timeout: 1000 })

      // Should show typing caret when animation is enabled
      await waitFor(() => {
        const carets = document.querySelectorAll('.typing-caret')
        expect(carets.length).toBeGreaterThan(0)
      })
    })

    it('should handle media query changes dynamically', async () => {
      let mediaQueryCallback: ((event: MediaQueryListEvent) => void) | null = null

      // Mock media query with ability to trigger changes
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: jest.fn().mockImplementation((query) => ({
          matches: false,
          media: query,
          onchange: null,
          addListener: jest.fn(),
          removeListener: jest.fn(),
          addEventListener: jest.fn((event, callback) => {
            if (event === 'change') {
              mediaQueryCallback = callback
            }
          }),
          removeEventListener: jest.fn(),
          dispatchEvent: jest.fn(),
        })),
      })

      render(<TypingTerminal lines={testLines} />)

      // Initially should have animation
      await waitFor(() => {
        const statusRegion = screen.getByRole('status')
        expect(statusRegion.textContent).toMatch(/Typing:/)
      }, { timeout: 1000 })

      // Simulate user changing to reduced motion
      if (mediaQueryCallback) {
        act(() => {
          mediaQueryCallback({ matches: true } as MediaQueryListEvent)
        })
      }

      // Should adapt to reduced motion preference
      await waitFor(() => {
        const statusRegion = screen.getByRole('status')
        expect(statusRegion.textContent).toContain('Terminal output:')
      })
    })
  })

  describe('ARIA Attributes and Semantic Structure', () => {
    it('should have proper ARIA hidden attributes on decorative elements', async () => {
      render(<TypingTerminal lines={testLines} />)

      // Wait for typing to start
      await waitFor(() => {
        const carets = document.querySelectorAll('.typing-caret')
        if (carets.length > 0) {
          carets.forEach(caret => {
            expect(caret).toHaveAttribute('aria-hidden', 'true')
          })
        }
      }, { timeout: 1000 })
    })

    it('should maintain semantic structure with proper roles', () => {
      render(<TypingTerminal lines={testLines} />)

      // Should have exactly one status role for screen reader announcements
      const statusElements = screen.getAllByRole('status')
      expect(statusElements).toHaveLength(1)

      // Status element should be properly configured
      const statusElement = statusElements[0]
      expect(statusElement).toHaveAttribute('aria-live', 'polite')
      expect(statusElement).toHaveAttribute('aria-label', 'Terminal output')
    })

    it('should handle bold line indicators accessibly', async () => {
      const boldLineIndex = 1
      const boldIndicator = '>>>'

      render(
        <TypingTerminal
          lines={testLines}
          boldLineIndex={boldLineIndex}
          boldLineIndicator={boldIndicator}
        />
      )

      // Wait for initialization
      await waitFor(() => {
        const statusRegion = screen.getByRole('status')
        expect(statusRegion).not.toHaveTextContent('Initializing diagnostic agent...')
      })

      // Bold indicators should be included in the visual display but not interfere with screen reader content
      const terminalContainer = document.querySelector('[aria-hidden="true"]')
      expect(terminalContainer).toBeInTheDocument()
    })
  })

  describe('Loading State Accessibility', () => {
    it('should announce loading state properly', () => {
      const customLoadingText = 'Preparing terminal interface...'
      render(<TypingTerminal lines={testLines} loadingText={customLoadingText} />)

      const statusRegion = screen.getByRole('status')
      expect(statusRegion).toHaveTextContent(customLoadingText)

      // Loading spinner should be present in visual display
      const loadingSpinner = screen.getByTestId('loading-spinner')
      expect(loadingSpinner).toBeInTheDocument()
    })

    it('should transition from loading to content smoothly', async () => {
      render(<TypingTerminal lines={testLines} />)

      const statusRegion = screen.getByRole('status')

      // Initially should show loading
      expect(statusRegion).toHaveTextContent('Initializing diagnostic agent...')

      // Should transition to typing content
      await waitFor(() => {
        expect(statusRegion).not.toHaveTextContent('Initializing diagnostic agent...')
      })

      // Should show either typing progress or full content
      await waitFor(() => {
        const content = statusRegion.textContent || ''
        expect(content.length).toBeGreaterThan(0)
        expect(content).toMatch(/(Typing:|Terminal output:)/)
      })
    })
  })

  describe('Edge Cases and Error Handling', () => {
    it('should handle empty lines array gracefully', () => {
      render(<TypingTerminal lines={[]} />)

      const statusRegion = screen.getByRole('status')
      expect(statusRegion).toBeInTheDocument()
      expect(statusRegion).toHaveAttribute('aria-live', 'polite')
    })

    it('should handle undefined or null lines gracefully', async () => {
      const linesWithUndefined = ['Valid line', undefined as any, 'Another valid line']
      
      render(<TypingTerminal lines={linesWithUndefined} />)

      const statusRegion = screen.getByRole('status')
      expect(statusRegion).toBeInTheDocument()

      // Should not crash and should handle the undefined line
      await waitFor(() => {
        expect(statusRegion).not.toHaveTextContent('Initializing diagnostic agent...')
      })
    })

    it('should maintain accessibility when component updates', async () => {
      const { rerender } = render(<TypingTerminal lines={testLines} />)

      // Initial accessibility check
      expect(screen.getByRole('status')).toBeInTheDocument()

      // Update with new lines
      const newLines = ['Updated line 1', 'Updated line 2']
      rerender(<TypingTerminal lines={newLines} />)

      // Should maintain accessibility structure
      expect(screen.getByRole('status')).toBeInTheDocument()
      expect(screen.getByRole('status')).toHaveAttribute('aria-live', 'polite')
      expect(screen.getByRole('status')).toHaveAttribute('aria-label', 'Terminal output')
    })
  })
})
