<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1755666043903" clover="3.2.0">
  <project timestamp="1755666043904" name="All files">
    <metrics statements="430" coveredstatements="68" conditionals="177" coveredconditionals="42" methods="119" coveredmethods="20" elements="726" coveredelements="130" complexity="0" loc="430" ncloc="430" packages="5" files="15" classes="15"/>
    <package name="components">
      <metrics statements="124" coveredstatements="0" conditionals="37" coveredconditionals="0" methods="48" coveredmethods="0"/>
      <file name="error-boundary-test.tsx" path="/Users/<USER>/Downloads/Code/go42/components/error-boundary-test.tsx">
        <metrics statements="13" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="13" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="36" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
      </file>
      <file name="error-boundary.tsx" path="/Users/<USER>/Downloads/Code/go42/components/error-boundary.tsx">
        <metrics statements="47" coveredstatements="0" conditionals="23" coveredconditionals="0" methods="13" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="68" count="0" type="stmt"/>
        <line num="72" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="88" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="93" count="0" type="stmt"/>
        <line num="98" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="99" count="0" type="stmt"/>
        <line num="104" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="105" count="0" type="stmt"/>
        <line num="110" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="111" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="126" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="127" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="135" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="137" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="138" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="207" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="209" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
      </file>
      <file name="root-error-boundary.tsx" path="/Users/<USER>/Downloads/Code/go42/components/root-error-boundary.tsx">
        <metrics statements="6" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
      </file>
      <file name="section-error-boundaries.tsx" path="/Users/<USER>/Downloads/Code/go42/components/section-error-boundaries.tsx">
        <metrics statements="19" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="15" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
      </file>
      <file name="theme-provider.tsx" path="/Users/<USER>/Downloads/Code/go42/components/theme-provider.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
      </file>
      <file name="use-cases.tsx" path="/Users/<USER>/Downloads/Code/go42/components/use-cases.tsx">
        <metrics statements="36" coveredstatements="0" conditionals="12" coveredconditionals="0" methods="11" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="119" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
      </file>
    </package>
    <package name="components.layout">
      <metrics statements="8" coveredstatements="0" conditionals="9" coveredconditionals="0" methods="3" coveredmethods="0"/>
      <file name="Navigation.tsx" path="/Users/<USER>/Downloads/Code/go42/components/layout/Navigation.tsx">
        <metrics statements="8" coveredstatements="0" conditionals="9" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
      </file>
    </package>
    <package name="components.ui">
      <metrics statements="65" coveredstatements="47" conditionals="46" coveredconditionals="23" methods="27" coveredmethods="15"/>
      <file name="TypingTerminal.tsx" path="/Users/<USER>/Downloads/Code/go42/components/ui/TypingTerminal.tsx">
        <metrics statements="48" coveredstatements="47" conditionals="29" coveredconditionals="23" methods="15" coveredmethods="15"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="12" count="96" type="stmt"/>
        <line num="14" count="96" type="stmt"/>
        <line num="16" count="15" type="cond" truecount="0" falsecount="1"/>
        <line num="18" count="15" type="stmt"/>
        <line num="19" count="15" type="stmt"/>
        <line num="21" count="15" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="25" count="15" type="stmt"/>
        <line num="26" count="15" type="stmt"/>
        <line num="29" count="96" type="stmt"/>
        <line num="54" count="16" type="stmt"/>
        <line num="61" count="96" type="stmt"/>
        <line num="62" count="96" type="stmt"/>
        <line num="63" count="96" type="stmt"/>
        <line num="66" count="96" type="stmt"/>
        <line num="69" count="96" type="stmt"/>
        <line num="72" count="96" type="stmt"/>
        <line num="73" count="18" type="stmt"/>
        <line num="74" count="71" type="stmt"/>
        <line num="76" count="18" type="stmt"/>
        <line num="78" count="18" type="stmt"/>
        <line num="81" count="18" type="cond" truecount="1" falsecount="0"/>
        <line num="82" count="2" type="stmt"/>
        <line num="84" count="2" type="stmt"/>
        <line num="87" count="16" type="stmt"/>
        <line num="89" count="16" type="stmt"/>
        <line num="90" count="16" type="stmt"/>
        <line num="91" count="15" type="cond" truecount="0" falsecount="1"/>
        <line num="92" count="15" type="stmt"/>
        <line num="93" count="15" type="stmt"/>
        <line num="94" count="15" type="cond" truecount="0" falsecount="1"/>
        <line num="95" count="15" type="stmt"/>
        <line num="96" count="82" type="cond" truecount="1" falsecount="0"/>
        <line num="97" count="70" type="stmt"/>
        <line num="98" count="69" type="stmt"/>
        <line num="99" count="69" type="stmt"/>
        <line num="100" count="69" type="stmt"/>
        <line num="102" count="70" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="107" count="1" type="stmt"/>
        <line num="108" count="1" type="stmt"/>
        <line num="109" count="1" type="stmt"/>
        <line num="113" count="18" type="stmt"/>
        <line num="114" count="18" type="stmt"/>
        <line num="155" count="27" type="stmt"/>
      </file>
      <file name="button.tsx" path="/Users/<USER>/Downloads/Code/go42/components/ui/button.tsx">
        <metrics statements="7" coveredstatements="0" conditionals="3" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="56" count="0" type="stmt"/>
      </file>
      <file name="card.tsx" path="/Users/<USER>/Downloads/Code/go42/components/ui/card.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
      </file>
      <file name="loading-spinner.tsx" path="/Users/<USER>/Downloads/Code/go42/components/ui/loading-spinner.tsx">
        <metrics statements="7" coveredstatements="0" conditionals="14" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="70" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="74" count="0" type="stmt"/>
      </file>
    </package>
    <package name="hooks">
      <metrics statements="208" coveredstatements="0" conditionals="66" coveredconditionals="0" methods="34" coveredmethods="0"/>
      <file name="spotlight.tsx" path="/Users/<USER>/Downloads/Code/go42/hooks/spotlight.tsx">
        <metrics statements="208" coveredstatements="0" conditionals="66" coveredconditionals="0" methods="34" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="54" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="72" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="86" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="87" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="97" count="0" type="stmt"/>
        <line num="99" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="108" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="111" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="135" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="148" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="160" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="168" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="186" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="188" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="191" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="193" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="213" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="214" count="0" type="stmt"/>
        <line num="215" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="216" count="0" type="stmt"/>
        <line num="217" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="226" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="227" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="290" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="292" count="0" type="stmt"/>
        <line num="293" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="299" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="301" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="304" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="309" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="311" count="0" type="stmt"/>
        <line num="312" count="0" type="stmt"/>
        <line num="313" count="0" type="stmt"/>
        <line num="314" count="0" type="stmt"/>
        <line num="316" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="321" count="0" type="stmt"/>
        <line num="322" count="0" type="stmt"/>
        <line num="324" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="329" count="0" type="stmt"/>
        <line num="330" count="0" type="stmt"/>
        <line num="331" count="0" type="stmt"/>
        <line num="333" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="334" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="336" count="0" type="stmt"/>
        <line num="337" count="0" type="stmt"/>
        <line num="339" count="0" type="stmt"/>
        <line num="340" count="0" type="stmt"/>
        <line num="341" count="0" type="stmt"/>
        <line num="343" count="0" type="stmt"/>
        <line num="347" count="0" type="stmt"/>
        <line num="348" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="349" count="0" type="stmt"/>
        <line num="350" count="0" type="stmt"/>
        <line num="354" count="0" type="stmt"/>
        <line num="355" count="0" type="stmt"/>
        <line num="356" count="0" type="stmt"/>
        <line num="358" count="0" type="stmt"/>
        <line num="364" count="0" type="stmt"/>
        <line num="365" count="0" type="stmt"/>
        <line num="367" count="0" type="stmt"/>
        <line num="369" count="0" type="stmt"/>
        <line num="371" count="0" type="stmt"/>
        <line num="372" count="0" type="stmt"/>
        <line num="373" count="0" type="stmt"/>
        <line num="376" count="0" type="stmt"/>
        <line num="377" count="0" type="stmt"/>
        <line num="378" count="0" type="stmt"/>
        <line num="380" count="0" type="stmt"/>
        <line num="381" count="0" type="stmt"/>
        <line num="383" count="0" type="stmt"/>
        <line num="384" count="0" type="stmt"/>
        <line num="385" count="0" type="stmt"/>
        <line num="386" count="0" type="stmt"/>
        <line num="387" count="0" type="stmt"/>
        <line num="388" count="0" type="stmt"/>
        <line num="389" count="0" type="stmt"/>
        <line num="407" count="0" type="stmt"/>
        <line num="413" count="0" type="stmt"/>
        <line num="419" count="0" type="stmt"/>
      </file>
    </package>
    <package name="lib">
      <metrics statements="25" coveredstatements="21" conditionals="19" coveredconditionals="19" methods="7" coveredmethods="5"/>
      <file name="constants.ts" path="/Users/<USER>/Downloads/Code/go42/lib/constants.ts">
        <metrics statements="4" coveredstatements="4" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="7" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="29" count="6201" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
      </file>
      <file name="error-logger.ts" path="/Users/<USER>/Downloads/Code/go42/lib/error-logger.ts">
        <metrics statements="17" coveredstatements="17" conditionals="19" coveredconditionals="19" methods="6" coveredmethods="5"/>
        <line num="33" count="10" type="stmt"/>
        <line num="43" count="17" type="stmt"/>
        <line num="46" count="17" type="stmt"/>
        <line num="65" count="17" type="cond" truecount="4" falsecount="0"/>
        <line num="66" count="17" type="stmt"/>
        <line num="69" count="17" type="cond" truecount="1" falsecount="0"/>
        <line num="83" count="3" type="stmt"/>
        <line num="84" count="3" type="cond" truecount="3" falsecount="0"/>
        <line num="85" count="2" type="stmt"/>
        <line num="109" count="1" type="stmt"/>
        <line num="114" count="1" type="stmt"/>
        <line num="131" count="2" type="stmt"/>
        <line num="136" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="152" count="2" type="stmt"/>
        <line num="157" count="2" type="stmt"/>
        <line num="171" count="2" type="stmt"/>
        <line num="176" count="2" type="stmt"/>
      </file>
      <file name="utils.ts" path="/Users/<USER>/Downloads/Code/go42/lib/utils.ts">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
