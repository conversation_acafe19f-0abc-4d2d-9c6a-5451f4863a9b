{"/Users/<USER>/Downloads/Code/go42/components/error-boundary-test.tsx": {"path": "/Users/<USER>/Downloads/Code/go42/components/error-boundary-test.tsx", "statementMap": {"0": {"start": {"line": 31, "column": 16}, "end": {"line": 31, "column": null}}, "1": {"start": {"line": 3, "column": 32}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 4, "column": 23}, "end": {"line": 4, "column": null}}, "3": {"start": {"line": 5, "column": 30}, "end": {"line": 5, "column": null}}, "4": {"start": {"line": 12, "column": 2}, "end": {"line": 14, "column": null}}, "5": {"start": {"line": 13, "column": 4}, "end": {"line": 13, "column": null}}, "6": {"start": {"line": 32, "column": 40}, "end": {"line": 32, "column": null}}, "7": {"start": {"line": 35, "column": 2}, "end": {"line": 37, "column": null}}, "8": {"start": {"line": 36, "column": 4}, "end": {"line": 36, "column": null}}, "9": {"start": {"line": 46, "column": 12}, "end": {"line": 46, "column": null}}, "10": {"start": {"line": 47, "column": 12}, "end": {"line": 47, "column": null}}, "11": {"start": {"line": 55, "column": 27}, "end": {"line": 55, "column": null}}, "12": {"start": {"line": 63, "column": 27}, "end": {"line": 63, "column": null}}}, "fnMap": {"0": {"name": "ErrorThrowingComponent", "decl": {"start": {"line": 11, "column": 9}, "end": {"line": 11, "column": 32}}, "loc": {"start": {"line": 11, "column": 73}, "end": {"line": 25, "column": null}}}, "1": {"name": "ErrorBoundaryTest", "decl": {"start": {"line": 31, "column": 16}, "end": {"line": 31, "column": null}}, "loc": {"start": {"line": 31, "column": 16}, "end": {"line": 78, "column": null}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 45, "column": 19}, "end": {"line": 45, "column": 20}}, "loc": {"start": {"line": 45, "column": 27}, "end": {"line": 48, "column": null}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 55, "column": 21}, "end": {"line": 55, "column": 27}}, "loc": {"start": {"line": 55, "column": 27}, "end": {"line": 55, "column": null}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 63, "column": 21}, "end": {"line": 63, "column": 27}}, "loc": {"start": {"line": 63, "column": 27}, "end": {"line": 63, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 12, "column": 2}, "end": {"line": 14, "column": null}}, "type": "if", "locations": [{"start": {"line": 12, "column": 2}, "end": {"line": 14, "column": null}}]}, "1": {"loc": {"start": {"line": 35, "column": 2}, "end": {"line": 37, "column": null}}, "type": "if", "locations": [{"start": {"line": 35, "column": 2}, "end": {"line": 37, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {"0": [0], "1": [0]}}, "/Users/<USER>/Downloads/Code/go42/components/error-boundary.tsx": {"path": "/Users/<USER>/Downloads/Code/go42/components/error-boundary.tsx", "statementMap": {"0": {"start": {"line": 37, "column": 13}, "end": {"line": 37, "column": 26}}, "1": {"start": {"line": 215, "column": 16}, "end": {"line": 215, "column": 31}}, "2": {"start": {"line": 197, "column": 16}, "end": {"line": 197, "column": 33}}, "3": {"start": {"line": 3, "column": 44}, "end": {"line": 3, "column": null}}, "4": {"start": {"line": 4, "column": 23}, "end": {"line": 4, "column": null}}, "5": {"start": {"line": 5, "column": 21}, "end": {"line": 5, "column": null}}, "6": {"start": {"line": 6, "column": 38}, "end": {"line": 6, "column": null}}, "7": {"start": {"line": 41, "column": 4}, "end": {"line": 129, "column": null}}, "8": {"start": {"line": 110, "column": 4}, "end": {"line": 112, "column": null}}, "9": {"start": {"line": 111, "column": 6}, "end": {"line": 111, "column": null}}, "10": {"start": {"line": 114, "column": 4}, "end": {"line": 118, "column": null}}, "11": {"start": {"line": 122, "column": 4}, "end": {"line": 122, "column": null}}, "12": {"start": {"line": 126, "column": 4}, "end": {"line": 128, "column": null}}, "13": {"start": {"line": 127, "column": 6}, "end": {"line": 127, "column": null}}, "14": {"start": {"line": 42, "column": 4}, "end": {"line": 46, "column": null}}, "15": {"start": {"line": 51, "column": 4}, "end": {"line": 54, "column": null}}, "16": {"start": {"line": 59, "column": 4}, "end": {"line": 59, "column": null}}, "17": {"start": {"line": 62, "column": 4}, "end": {"line": 64, "column": null}}, "18": {"start": {"line": 67, "column": 4}, "end": {"line": 69, "column": null}}, "19": {"start": {"line": 68, "column": 6}, "end": {"line": 68, "column": null}}, "20": {"start": {"line": 72, "column": 4}, "end": {"line": 80, "column": null}}, "21": {"start": {"line": 75, "column": 6}, "end": {"line": 75, "column": null}}, "22": {"start": {"line": 76, "column": 6}, "end": {"line": 76, "column": null}}, "23": {"start": {"line": 77, "column": 6}, "end": {"line": 77, "column": null}}, "24": {"start": {"line": 78, "column": 6}, "end": {"line": 78, "column": null}}, "25": {"start": {"line": 79, "column": 6}, "end": {"line": 79, "column": null}}, "26": {"start": {"line": 84, "column": 46}, "end": {"line": 84, "column": 56}}, "27": {"start": {"line": 85, "column": 25}, "end": {"line": 85, "column": 35}}, "28": {"start": {"line": 88, "column": 4}, "end": {"line": 95, "column": null}}, "29": {"start": {"line": 89, "column": 33}, "end": {"line": 90, "column": null}}, "30": {"start": {"line": 90, "column": 24}, "end": {"line": 90, "column": 60}}, "31": {"start": {"line": 92, "column": 6}, "end": {"line": 94, "column": null}}, "32": {"start": {"line": 93, "column": 8}, "end": {"line": 93, "column": null}}, "33": {"start": {"line": 98, "column": 4}, "end": {"line": 100, "column": null}}, "34": {"start": {"line": 99, "column": 6}, "end": {"line": 99, "column": null}}, "35": {"start": {"line": 104, "column": 4}, "end": {"line": 106, "column": null}}, "36": {"start": {"line": 105, "column": 6}, "end": {"line": 105, "column": null}}, "37": {"start": {"line": 132, "column": 32}, "end": {"line": 132, "column": 42}}, "38": {"start": {"line": 133, "column": 35}, "end": {"line": 133, "column": 45}}, "39": {"start": {"line": 135, "column": 4}, "end": {"line": 188, "column": null}}, "40": {"start": {"line": 137, "column": 6}, "end": {"line": 139, "column": null}}, "41": {"start": {"line": 138, "column": 8}, "end": {"line": 138, "column": null}}, "42": {"start": {"line": 190, "column": 4}, "end": {"line": 190, "column": null}}, "43": {"start": {"line": 207, "column": 2}, "end": {"line": 207, "column": null}}, "44": {"start": {"line": 209, "column": 2}, "end": {"line": 209, "column": null}}, "45": {"start": {"line": 216, "column": 2}, "end": {"line": 219, "column": null}}, "46": {"start": {"line": 218, "column": 4}, "end": {"line": 218, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_7)", "decl": {"start": {"line": 40, "column": 2}, "end": {"line": 40, "column": 14}}, "loc": {"start": {"line": 40, "column": 41}, "end": {"line": 47, "column": null}}}, "1": {"name": "(anonymous_8)", "decl": {"start": {"line": 109, "column": 23}, "end": {"line": 109, "column": null}}, "loc": {"start": {"line": 109, "column": 23}, "end": {"line": 119, "column": null}}}, "2": {"name": "(anonymous_9)", "decl": {"start": {"line": 121, "column": 16}, "end": {"line": 121, "column": null}}, "loc": {"start": {"line": 121, "column": 16}, "end": {"line": 123, "column": null}}}, "3": {"name": "(anonymous_10)", "decl": {"start": {"line": 125, "column": 17}, "end": {"line": 125, "column": null}}, "loc": {"start": {"line": 125, "column": 17}, "end": {"line": 129, "column": null}}}, "4": {"name": "(anonymous_11)", "decl": {"start": {"line": 49, "column": 2}, "end": {"line": 49, "column": 9}}, "loc": {"start": {"line": 49, "column": 77}, "end": {"line": 55, "column": null}}}, "5": {"name": "(anonymous_12)", "decl": {"start": {"line": 57, "column": 2}, "end": {"line": 57, "column": 20}}, "loc": {"start": {"line": 57, "column": 56}, "end": {"line": 81, "column": null}}}, "6": {"name": "(anonymous_13)", "decl": {"start": {"line": 83, "column": 2}, "end": {"line": 83, "column": 21}}, "loc": {"start": {"line": 83, "column": 52}, "end": {"line": 101, "column": null}}}, "7": {"name": "(anonymous_14)", "decl": {"start": {"line": 90, "column": 8}, "end": {"line": 90, "column": 9}}, "loc": {"start": {"line": 90, "column": 24}, "end": {"line": 90, "column": 60}}}, "8": {"name": "(anonymous_15)", "decl": {"start": {"line": 103, "column": 2}, "end": {"line": 103, "column": 25}}, "loc": {"start": {"line": 103, "column": 25}, "end": {"line": 107, "column": null}}}, "9": {"name": "(anonymous_16)", "decl": {"start": {"line": 131, "column": 2}, "end": {"line": 131, "column": 11}}, "loc": {"start": {"line": 131, "column": 11}, "end": {"line": 191, "column": null}}}, "10": {"name": "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "decl": {"start": {"line": 197, "column": 16}, "end": {"line": 197, "column": 33}}, "loc": {"start": {"line": 199, "column": 59}, "end": {"line": 210, "column": null}}}, "11": {"name": "useErrorHandler", "decl": {"start": {"line": 215, "column": 16}, "end": {"line": 215, "column": 31}}, "loc": {"start": {"line": 215, "column": 16}, "end": {"line": 220, "column": null}}}, "12": {"name": "(anonymous_20)", "decl": {"start": {"line": 216, "column": 27}, "end": {"line": 216, "column": 28}}, "loc": {"start": {"line": 216, "column": 28}, "end": {"line": 219, "column": 5}}}}, "branchMap": {"0": {"loc": {"start": {"line": 110, "column": 4}, "end": {"line": 112, "column": null}}, "type": "if", "locations": [{"start": {"line": 110, "column": 4}, "end": {"line": 112, "column": null}}]}, "1": {"loc": {"start": {"line": 126, "column": 4}, "end": {"line": 128, "column": null}}, "type": "if", "locations": [{"start": {"line": 126, "column": 4}, "end": {"line": 128, "column": null}}]}, "2": {"loc": {"start": {"line": 67, "column": 4}, "end": {"line": 69, "column": null}}, "type": "if", "locations": [{"start": {"line": 67, "column": 4}, "end": {"line": 69, "column": null}}]}, "3": {"loc": {"start": {"line": 72, "column": 4}, "end": {"line": 80, "column": null}}, "type": "if", "locations": [{"start": {"line": 72, "column": 4}, "end": {"line": 80, "column": null}}]}, "4": {"loc": {"start": {"line": 72, "column": 8}, "end": {"line": 72, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 72, "column": 8}, "end": {"line": 72, "column": 41}}, {"start": {"line": 72, "column": 41}, "end": {"line": 72, "column": 55}}]}, "5": {"loc": {"start": {"line": 88, "column": 4}, "end": {"line": 95, "column": null}}, "type": "if", "locations": [{"start": {"line": 88, "column": 4}, "end": {"line": 95, "column": null}}]}, "6": {"loc": {"start": {"line": 88, "column": 8}, "end": {"line": 88, "column": 52}}, "type": "binary-expr", "locations": [{"start": {"line": 88, "column": 8}, "end": {"line": 88, "column": 20}}, {"start": {"line": 88, "column": 20}, "end": {"line": 88, "column": 33}}, {"start": {"line": 88, "column": 33}, "end": {"line": 88, "column": 52}}]}, "7": {"loc": {"start": {"line": 92, "column": 6}, "end": {"line": 94, "column": null}}, "type": "if", "locations": [{"start": {"line": 92, "column": 6}, "end": {"line": 94, "column": null}}]}, "8": {"loc": {"start": {"line": 98, "column": 4}, "end": {"line": 100, "column": null}}, "type": "if", "locations": [{"start": {"line": 98, "column": 4}, "end": {"line": 100, "column": null}}]}, "9": {"loc": {"start": {"line": 98, "column": 8}, "end": {"line": 98, "column": 84}}, "type": "binary-expr", "locations": [{"start": {"line": 98, "column": 8}, "end": {"line": 98, "column": 20}}, {"start": {"line": 98, "column": 20}, "end": {"line": 98, "column": 42}}, {"start": {"line": 98, "column": 42}, "end": {"line": 98, "column": 84}}]}, "10": {"loc": {"start": {"line": 104, "column": 4}, "end": {"line": 106, "column": null}}, "type": "if", "locations": [{"start": {"line": 104, "column": 4}, "end": {"line": 106, "column": null}}]}, "11": {"loc": {"start": {"line": 135, "column": 4}, "end": {"line": 188, "column": null}}, "type": "if", "locations": [{"start": {"line": 135, "column": 4}, "end": {"line": 188, "column": null}}]}, "12": {"loc": {"start": {"line": 137, "column": 6}, "end": {"line": 139, "column": null}}, "type": "if", "locations": [{"start": {"line": 137, "column": 6}, "end": {"line": 139, "column": null}}]}, "13": {"loc": {"start": {"line": 153, "column": 15}, "end": {"line": 153, "column": 57}}, "type": "binary-expr", "locations": [{"start": {"line": 153, "column": 15}, "end": {"line": 153, "column": 57}}, {"start": {"line": 153, "column": 57}, "end": {"line": 153, "column": null}}]}, "14": {"loc": {"start": {"line": 162, "column": 21}, "end": {"line": 162, "column": 32}}, "type": "binary-expr", "locations": [{"start": {"line": 162, "column": 21}, "end": {"line": 162, "column": 32}}]}, "15": {"loc": {"start": {"line": 207, "column": 54}, "end": {"line": 207, "column": 93}}, "type": "binary-expr", "locations": [{"start": {"line": 207, "column": 54}, "end": {"line": 207, "column": 75}}, {"start": {"line": 207, "column": 79}, "end": {"line": 207, "column": 93}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0], "4": [0, 0], "5": [0], "6": [0, 0, 0], "7": [0], "8": [0], "9": [0, 0, 0], "10": [0], "11": [0], "12": [0], "13": [0, 0], "14": [0], "15": [0, 0]}}, "/Users/<USER>/Downloads/Code/go42/components/root-error-boundary.tsx": {"path": "/Users/<USER>/Downloads/Code/go42/components/root-error-boundary.tsx", "statementMap": {"0": {"start": {"line": 11, "column": 16}, "end": {"line": 11, "column": 34}}, "1": {"start": {"line": 3, "column": 18}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 4, "column": 30}, "end": {"line": 4, "column": null}}, "3": {"start": {"line": 5, "column": 38}, "end": {"line": 5, "column": null}}, "4": {"start": {"line": 16, "column": 8}, "end": {"line": 16, "column": null}}, "5": {"start": {"line": 28, "column": 29}, "end": {"line": 28, "column": null}}}, "fnMap": {"0": {"name": "RootErrorBoundary", "decl": {"start": {"line": 11, "column": 16}, "end": {"line": 11, "column": 34}}, "loc": {"start": {"line": 11, "column": 77}, "end": {"line": 40, "column": null}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 14, "column": 15}, "end": {"line": 14, "column": 16}}, "loc": {"start": {"line": 14, "column": 23}, "end": {"line": 17, "column": null}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 28, "column": 23}, "end": {"line": 28, "column": 29}}, "loc": {"start": {"line": 28, "column": 29}, "end": {"line": 28, "column": null}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {}}, "/Users/<USER>/Downloads/Code/go42/components/section-error-boundaries.tsx": {"path": "/Users/<USER>/Downloads/Code/go42/components/section-error-boundaries.tsx", "statementMap": {"0": {"start": {"line": 198, "column": 16}, "end": {"line": 198, "column": 32}}, "1": {"start": {"line": 170, "column": 16}, "end": {"line": 170, "column": 37}}, "2": {"start": {"line": 11, "column": 16}, "end": {"line": 11, "column": 33}}, "3": {"start": {"line": 119, "column": 16}, "end": {"line": 119, "column": 39}}, "4": {"start": {"line": 43, "column": 16}, "end": {"line": 43, "column": 38}}, "5": {"start": {"line": 144, "column": 16}, "end": {"line": 144, "column": 44}}, "6": {"start": {"line": 70, "column": 16}, "end": {"line": 70, "column": 37}}, "7": {"start": {"line": 3, "column": 18}, "end": {"line": 3, "column": null}}, "8": {"start": {"line": 4, "column": 30}, "end": {"line": 4, "column": null}}, "9": {"start": {"line": 5, "column": 23}, "end": {"line": 5, "column": null}}, "10": {"start": {"line": 6, "column": 38}, "end": {"line": 6, "column": null}}, "11": {"start": {"line": 23, "column": 29}, "end": {"line": 23, "column": null}}, "12": {"start": {"line": 32, "column": 8}, "end": {"line": 32, "column": null}}, "13": {"start": {"line": 59, "column": 8}, "end": {"line": 59, "column": null}}, "14": {"start": {"line": 108, "column": 8}, "end": {"line": 108, "column": null}}, "15": {"start": {"line": 133, "column": 8}, "end": {"line": 133, "column": null}}, "16": {"start": {"line": 159, "column": 8}, "end": {"line": 159, "column": null}}, "17": {"start": {"line": 187, "column": 8}, "end": {"line": 187, "column": null}}, "18": {"start": {"line": 218, "column": 8}, "end": {"line": 218, "column": null}}}, "fnMap": {"0": {"name": "HeroError<PERSON><PERSON><PERSON><PERSON>", "decl": {"start": {"line": 11, "column": 16}, "end": {"line": 11, "column": 33}}, "loc": {"start": {"line": 11, "column": 77}, "end": {"line": 38, "column": null}}}, "1": {"name": "(anonymous_10)", "decl": {"start": {"line": 23, "column": 23}, "end": {"line": 23, "column": 29}}, "loc": {"start": {"line": 23, "column": 29}, "end": {"line": 23, "column": null}}}, "2": {"name": "(anonymous_11)", "decl": {"start": {"line": 31, "column": 15}, "end": {"line": 31, "column": 16}}, "loc": {"start": {"line": 31, "column": 23}, "end": {"line": 33, "column": null}}}, "3": {"name": "SpotlightErrorBoundary", "decl": {"start": {"line": 43, "column": 16}, "end": {"line": 43, "column": 38}}, "loc": {"start": {"line": 43, "column": 82}, "end": {"line": 65, "column": null}}}, "4": {"name": "(anonymous_13)", "decl": {"start": {"line": 58, "column": 15}, "end": {"line": 58, "column": 16}}, "loc": {"start": {"line": 58, "column": 23}, "end": {"line": 60, "column": null}}}, "5": {"name": "UseCasesErrorBoundary", "decl": {"start": {"line": 70, "column": 16}, "end": {"line": 70, "column": 37}}, "loc": {"start": {"line": 70, "column": 81}, "end": {"line": 114, "column": null}}}, "6": {"name": "(anonymous_15)", "decl": {"start": {"line": 107, "column": 15}, "end": {"line": 107, "column": 16}}, "loc": {"start": {"line": 107, "column": 23}, "end": {"line": 109, "column": null}}}, "7": {"name": "NavigationErrorBoundary", "decl": {"start": {"line": 119, "column": 16}, "end": {"line": 119, "column": 39}}, "loc": {"start": {"line": 119, "column": 83}, "end": {"line": 139, "column": null}}}, "8": {"name": "(anonymous_17)", "decl": {"start": {"line": 132, "column": 15}, "end": {"line": 132, "column": 16}}, "loc": {"start": {"line": 132, "column": 23}, "end": {"line": 134, "column": null}}}, "9": {"name": "TypingAnimationErrorBoundary", "decl": {"start": {"line": 144, "column": 16}, "end": {"line": 144, "column": 44}}, "loc": {"start": {"line": 144, "column": 88}, "end": {"line": 165, "column": null}}}, "10": {"name": "(anonymous_19)", "decl": {"start": {"line": 158, "column": 15}, "end": {"line": 158, "column": 16}}, "loc": {"start": {"line": 158, "column": 23}, "end": {"line": 160, "column": null}}}, "11": {"name": "FeaturesErrorBoundary", "decl": {"start": {"line": 170, "column": 16}, "end": {"line": 170, "column": 37}}, "loc": {"start": {"line": 170, "column": 81}, "end": {"line": 193, "column": null}}}, "12": {"name": "(anonymous_21)", "decl": {"start": {"line": 186, "column": 15}, "end": {"line": 186, "column": 16}}, "loc": {"start": {"line": 186, "column": 23}, "end": {"line": 188, "column": null}}}, "13": {"name": "CTAErrorBoundary", "decl": {"start": {"line": 198, "column": 16}, "end": {"line": 198, "column": 32}}, "loc": {"start": {"line": 198, "column": 76}, "end": {"line": 224, "column": null}}}, "14": {"name": "(anonymous_23)", "decl": {"start": {"line": 217, "column": 15}, "end": {"line": 217, "column": 16}}, "loc": {"start": {"line": 217, "column": 23}, "end": {"line": 219, "column": null}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0}, "b": {}}, "/Users/<USER>/Downloads/Code/go42/components/theme-provider.tsx": {"path": "/Users/<USER>/Downloads/Code/go42/components/theme-provider.tsx", "statementMap": {"0": {"start": {"line": 6, "column": 16}, "end": {"line": 6, "column": 30}}, "1": {"start": {"line": 3, "column": 23}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 4, "column": 77}, "end": {"line": 4, "column": null}}}, "fnMap": {"0": {"name": "ThemeProvider", "decl": {"start": {"line": 6, "column": 16}, "end": {"line": 6, "column": 30}}, "loc": {"start": {"line": 6, "column": 72}, "end": {"line": 8, "column": null}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0}, "b": {}}, "/Users/<USER>/Downloads/Code/go42/components/use-cases.tsx": {"path": "/Users/<USER>/Downloads/Code/go42/components/use-cases.tsx", "statementMap": {"0": {"start": {"line": 78, "column": 24}, "end": {"line": 78, "column": null}}, "1": {"start": {"line": 3, "column": 29}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 4, "column": 49}, "end": {"line": 4, "column": null}}, "3": {"start": {"line": 5, "column": 18}, "end": {"line": 5, "column": null}}, "4": {"start": {"line": 6, "column": 32}, "end": {"line": 6, "column": null}}, "5": {"start": {"line": 25, "column": 28}, "end": {"line": 63, "column": null}}, "6": {"start": {"line": 79, "column": 31}, "end": {"line": 79, "column": null}}, "7": {"start": {"line": 80, "column": 42}, "end": {"line": 80, "column": null}}, "8": {"start": {"line": 81, "column": 42}, "end": {"line": 82, "column": null}}, "9": {"start": {"line": 85, "column": 21}, "end": {"line": 85, "column": null}}, "10": {"start": {"line": 85, "column": 39}, "end": {"line": 85, "column": 63}}, "11": {"start": {"line": 86, "column": 21}, "end": {"line": 86, "column": null}}, "12": {"start": {"line": 86, "column": 39}, "end": {"line": 86, "column": 63}}, "13": {"start": {"line": 92, "column": 24}, "end": {"line": 102, "column": null}}, "14": {"start": {"line": 94, "column": 6}, "end": {"line": 100, "column": null}}, "15": {"start": {"line": 95, "column": 8}, "end": {"line": 95, "column": null}}, "16": {"start": {"line": 96, "column": 8}, "end": {"line": 96, "column": null}}, "17": {"start": {"line": 97, "column": 13}, "end": {"line": 100, "column": null}}, "18": {"start": {"line": 98, "column": 8}, "end": {"line": 98, "column": null}}, "19": {"start": {"line": 99, "column": 8}, "end": {"line": 99, "column": null}}, "20": {"start": {"line": 109, "column": 26}, "end": {"line": 115, "column": null}}, "21": {"start": {"line": 110, "column": 4}, "end": {"line": 114, "column": null}}, "22": {"start": {"line": 111, "column": 23}, "end": {"line": 111, "column": null}}, "23": {"start": {"line": 112, "column": 6}, "end": {"line": 112, "column": null}}, "24": {"start": {"line": 113, "column": 6}, "end": {"line": 113, "column": null}}, "25": {"start": {"line": 117, "column": 19}, "end": {"line": 120, "column": null}}, "26": {"start": {"line": 118, "column": 4}, "end": {"line": 118, "column": null}}, "27": {"start": {"line": 118, "column": 19}, "end": {"line": 118, "column": null}}, "28": {"start": {"line": 119, "column": 4}, "end": {"line": 119, "column": null}}, "29": {"start": {"line": 122, "column": 2}, "end": {"line": 133, "column": null}}, "30": {"start": {"line": 123, "column": 4}, "end": {"line": 123, "column": null}}, "31": {"start": {"line": 123, "column": 19}, "end": {"line": 123, "column": null}}, "32": {"start": {"line": 125, "column": 4}, "end": {"line": 125, "column": null}}, "33": {"start": {"line": 126, "column": 4}, "end": {"line": 126, "column": null}}, "34": {"start": {"line": 127, "column": 4}, "end": {"line": 127, "column": null}}, "35": {"start": {"line": 129, "column": 4}, "end": {"line": 132, "column": null}}, "36": {"start": {"line": 130, "column": 6}, "end": {"line": 130, "column": null}}, "37": {"start": {"line": 131, "column": 6}, "end": {"line": 131, "column": null}}, "38": {"start": {"line": 178, "column": 20}, "end": {"line": 178, "column": 35}}, "39": {"start": {"line": 192, "column": 38}, "end": {"line": 192, "column": null}}}, "fnMap": {"0": {"name": "UseCases", "decl": {"start": {"line": 78, "column": 24}, "end": {"line": 78, "column": null}}, "loc": {"start": {"line": 78, "column": 24}, "end": {"line": 232, "column": null}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 85, "column": 33}, "end": {"line": 85, "column": 39}}, "loc": {"start": {"line": 85, "column": 39}, "end": {"line": 85, "column": 63}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 86, "column": 33}, "end": {"line": 86, "column": 39}}, "loc": {"start": {"line": 86, "column": 39}, "end": {"line": 86, "column": 63}}}, "3": {"name": "(anonymous_5)", "decl": {"start": {"line": 93, "column": 4}, "end": {"line": 93, "column": 5}}, "loc": {"start": {"line": 93, "column": 5}, "end": {"line": 101, "column": null}}}, "4": {"name": "(anonymous_6)", "decl": {"start": {"line": 109, "column": 38}, "end": {"line": 109, "column": 39}}, "loc": {"start": {"line": 109, "column": 39}, "end": {"line": 115, "column": 5}}}, "5": {"name": "(anonymous_7)", "decl": {"start": {"line": 110, "column": 20}, "end": {"line": 110, "column": null}}, "loc": {"start": {"line": 110, "column": 20}, "end": {"line": 114, "column": null}}}, "6": {"name": "(anonymous_8)", "decl": {"start": {"line": 117, "column": 31}, "end": {"line": 117, "column": null}}, "loc": {"start": {"line": 117, "column": 31}, "end": {"line": 120, "column": 5}}}, "7": {"name": "(anonymous_9)", "decl": {"start": {"line": 122, "column": 12}, "end": {"line": 122, "column": null}}, "loc": {"start": {"line": 122, "column": 12}, "end": {"line": 133, "column": 5}}}, "8": {"name": "(anonymous_10)", "decl": {"start": {"line": 129, "column": 11}, "end": {"line": 129, "column": null}}, "loc": {"start": {"line": 129, "column": 11}, "end": {"line": 132, "column": null}}}, "9": {"name": "(anonymous_11)", "decl": {"start": {"line": 177, "column": 32}, "end": {"line": 177, "column": 33}}, "loc": {"start": {"line": 178, "column": 20}, "end": {"line": 178, "column": 35}}}, "10": {"name": "(anonymous_12)", "decl": {"start": {"line": 192, "column": 32}, "end": {"line": 192, "column": 38}}, "loc": {"start": {"line": 192, "column": 38}, "end": {"line": 192, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 94, "column": 6}, "end": {"line": 100, "column": null}}, "type": "if", "locations": [{"start": {"line": 94, "column": 6}, "end": {"line": 100, "column": null}}, {"start": {"line": 97, "column": 13}, "end": {"line": 100, "column": null}}]}, "1": {"loc": {"start": {"line": 97, "column": 13}, "end": {"line": 100, "column": null}}, "type": "if", "locations": [{"start": {"line": 97, "column": 13}, "end": {"line": 100, "column": null}}]}, "2": {"loc": {"start": {"line": 118, "column": 4}, "end": {"line": 118, "column": null}}, "type": "if", "locations": [{"start": {"line": 118, "column": 4}, "end": {"line": 118, "column": null}}]}, "3": {"loc": {"start": {"line": 123, "column": 4}, "end": {"line": 123, "column": null}}, "type": "if", "locations": [{"start": {"line": 123, "column": 4}, "end": {"line": 123, "column": null}}]}, "4": {"loc": {"start": {"line": 144, "column": 15}, "end": {"line": 145, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 144, "column": 15}, "end": {"line": 144, "column": null}}, {"start": {"line": 145, "column": 16}, "end": {"line": 145, "column": null}}]}, "5": {"loc": {"start": {"line": 179, "column": 23}, "end": {"line": 179, "column": 41}}, "type": "binary-expr", "locations": [{"start": {"line": 179, "column": 23}, "end": {"line": 179, "column": 41}}]}, "6": {"loc": {"start": {"line": 185, "column": 29}, "end": {"line": 185, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 185, "column": 29}, "end": {"line": 185, "column": 35}}, {"start": {"line": 185, "column": 39}, "end": {"line": 185, "column": null}}]}, "7": {"loc": {"start": {"line": 190, "column": 26}, "end": {"line": 190, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 190, "column": 46}, "end": {"line": 190, "column": 62}}, {"start": {"line": 190, "column": 62}, "end": {"line": 190, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}, "b": {"0": [0, 0], "1": [0], "2": [0], "3": [0], "4": [0, 0], "5": [0], "6": [0, 0], "7": [0, 0]}}, "/Users/<USER>/Downloads/Code/go42/components/layout/Navigation.tsx": {"path": "/Users/<USER>/Downloads/Code/go42/components/layout/Navigation.tsx", "statementMap": {"0": {"start": {"line": 133, "column": 16}, "end": {"line": 133, "column": 26}}, "1": {"start": {"line": 141, "column": 0}, "end": {"line": 141, "column": 15}}, "2": {"start": {"line": 3, "column": 18}, "end": {"line": 3, "column": null}}, "3": {"start": {"line": 4, "column": 18}, "end": {"line": 4, "column": null}}, "4": {"start": {"line": 5, "column": 23}, "end": {"line": 5, "column": null}}, "5": {"start": {"line": 6, "column": 40}, "end": {"line": 6, "column": null}}, "6": {"start": {"line": 33, "column": 39}, "end": {"line": 59, "column": null}}, "7": {"start": {"line": 96, "column": 10}, "end": {"line": 97, "column": null}}, "8": {"start": {"line": 141, "column": 15}, "end": {"line": 141, "column": 25}}}, "fnMap": {"0": {"name": "NavigationContent", "decl": {"start": {"line": 70, "column": 9}, "end": {"line": 70, "column": 27}}, "loc": {"start": {"line": 79, "column": 18}, "end": {"line": 125, "column": null}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 95, "column": 19}, "end": {"line": 95, "column": null}}, "loc": {"start": {"line": 96, "column": 10}, "end": {"line": 97, "column": null}}}, "2": {"name": "Navigation", "decl": {"start": {"line": 133, "column": 16}, "end": {"line": 133, "column": 26}}, "loc": {"start": {"line": 133, "column": 49}, "end": {"line": 139, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 71, "column": 2}, "end": {"line": 71, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 71, "column": 14}, "end": {"line": 71, "column": 16}}]}, "1": {"loc": {"start": {"line": 72, "column": 2}, "end": {"line": 72, "column": 22}}, "type": "default-arg", "locations": [{"start": {"line": 72, "column": 10}, "end": {"line": 72, "column": 22}}]}, "2": {"loc": {"start": {"line": 73, "column": 2}, "end": {"line": 73, "column": 31}}, "type": "default-arg", "locations": [{"start": {"line": 73, "column": 12}, "end": {"line": 73, "column": 31}}]}, "3": {"loc": {"start": {"line": 74, "column": 2}, "end": {"line": 74, "column": 21}}, "type": "default-arg", "locations": [{"start": {"line": 74, "column": 12}, "end": {"line": 74, "column": 21}}]}, "4": {"loc": {"start": {"line": 75, "column": 2}, "end": {"line": 75, "column": 29}}, "type": "default-arg", "locations": [{"start": {"line": 75, "column": 25}, "end": {"line": 75, "column": 29}}]}, "5": {"loc": {"start": {"line": 76, "column": 2}, "end": {"line": 76, "column": 29}}, "type": "default-arg", "locations": [{"start": {"line": 76, "column": 25}, "end": {"line": 76, "column": 29}}]}, "6": {"loc": {"start": {"line": 77, "column": 2}, "end": {"line": 77, "column": 32}}, "type": "default-arg", "locations": [{"start": {"line": 77, "column": 19}, "end": {"line": 77, "column": 32}}]}, "7": {"loc": {"start": {"line": 109, "column": 9}, "end": {"line": 109, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 109, "column": 9}, "end": {"line": 109, "column": null}}]}, "8": {"loc": {"start": {"line": 114, "column": 9}, "end": {"line": 114, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 114, "column": 9}, "end": {"line": 114, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0], "4": [0], "5": [0], "6": [0], "7": [0], "8": [0]}}, "/Users/<USER>/Downloads/Code/go42/components/ui/TypingTerminal.tsx": {"path": "/Users/<USER>/Downloads/Code/go42/components/ui/TypingTerminal.tsx", "statementMap": {"0": {"start": {"line": 54, "column": 16}, "end": {"line": 54, "column": 31}}, "1": {"start": {"line": 3, "column": 52}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 4, "column": 33}, "end": {"line": 4, "column": null}}, "3": {"start": {"line": 5, "column": 31}, "end": {"line": 5, "column": null}}, "4": {"start": {"line": 12, "column": 58}, "end": {"line": 12, "column": null}}, "5": {"start": {"line": 14, "column": 2}, "end": {"line": 27, "column": null}}, "6": {"start": {"line": 16, "column": 4}, "end": {"line": 16, "column": null}}, "7": {"start": {"line": 16, "column": 39}, "end": {"line": 16, "column": null}}, "8": {"start": {"line": 18, "column": 23}, "end": {"line": 18, "column": null}}, "9": {"start": {"line": 19, "column": 4}, "end": {"line": 19, "column": null}}, "10": {"start": {"line": 21, "column": 25}, "end": {"line": 23, "column": null}}, "11": {"start": {"line": 22, "column": 6}, "end": {"line": 22, "column": null}}, "12": {"start": {"line": 25, "column": 4}, "end": {"line": 25, "column": null}}, "13": {"start": {"line": 26, "column": 4}, "end": {"line": 26, "column": null}}, "14": {"start": {"line": 26, "column": 17}, "end": {"line": 26, "column": null}}, "15": {"start": {"line": 29, "column": 2}, "end": {"line": 29, "column": null}}, "16": {"start": {"line": 61, "column": 38}, "end": {"line": 61, "column": null}}, "17": {"start": {"line": 61, "column": 63}, "end": {"line": 61, "column": null}}, "18": {"start": {"line": 62, "column": 38}, "end": {"line": 62, "column": null}}, "19": {"start": {"line": 63, "column": 56}, "end": {"line": 63, "column": null}}, "20": {"start": {"line": 66, "column": 31}, "end": {"line": 66, "column": null}}, "21": {"start": {"line": 69, "column": 21}, "end": {"line": 69, "column": null}}, "22": {"start": {"line": 69, "column": 35}, "end": {"line": 69, "column": 69}}, "23": {"start": {"line": 72, "column": 2}, "end": {"line": 116, "column": null}}, "24": {"start": {"line": 73, "column": 20}, "end": {"line": 73, "column": null}}, "25": {"start": {"line": 74, "column": 18}, "end": {"line": 74, "column": null}}, "26": {"start": {"line": 74, "column": 34}, "end": {"line": 74, "column": null}}, "27": {"start": {"line": 74, "column": 51}, "end": {"line": 74, "column": null}}, "28": {"start": {"line": 76, "column": 6}, "end": {"line": 111, "column": null}}, "29": {"start": {"line": 78, "column": 6}, "end": {"line": 78, "column": null}}, "30": {"start": {"line": 78, "column": 23}, "end": {"line": 78, "column": 53}}, "31": {"start": {"line": 81, "column": 6}, "end": {"line": 85, "column": null}}, "32": {"start": {"line": 82, "column": 8}, "end": {"line": 82, "column": null}}, "33": {"start": {"line": 84, "column": 8}, "end": {"line": 84, "column": null}}, "34": {"start": {"line": 87, "column": 6}, "end": {"line": 110, "column": null}}, "35": {"start": {"line": 89, "column": 8}, "end": {"line": 89, "column": null}}, "36": {"start": {"line": 90, "column": 8}, "end": {"line": 105, "column": null}}, "37": {"start": {"line": 90, "column": 21}, "end": {"line": 90, "column": 24}}, "38": {"start": {"line": 91, "column": 10}, "end": {"line": 91, "column": null}}, "39": {"start": {"line": 91, "column": 25}, "end": {"line": 91, "column": null}}, "40": {"start": {"line": 92, "column": 10}, "end": {"line": 92, "column": null}}, "41": {"start": {"line": 93, "column": 23}, "end": {"line": 93, "column": 31}}, "42": {"start": {"line": 94, "column": 10}, "end": {"line": 94, "column": 30}}, "43": {"start": {"line": 94, "column": 21}, "end": {"line": 94, "column": 30}}, "44": {"start": {"line": 95, "column": 10}, "end": {"line": 103, "column": null}}, "45": {"start": {"line": 95, "column": 23}, "end": {"line": 95, "column": 26}}, "46": {"start": {"line": 96, "column": 12}, "end": {"line": 96, "column": null}}, "47": {"start": {"line": 96, "column": 27}, "end": {"line": 96, "column": null}}, "48": {"start": {"line": 97, "column": 12}, "end": {"line": 101, "column": null}}, "49": {"start": {"line": 98, "column": 27}, "end": {"line": 98, "column": 37}}, "50": {"start": {"line": 99, "column": 14}, "end": {"line": 99, "column": null}}, "51": {"start": {"line": 100, "column": 14}, "end": {"line": 100, "column": null}}, "52": {"start": {"line": 102, "column": 12}, "end": {"line": 102, "column": null}}, "53": {"start": {"line": 104, "column": 10}, "end": {"line": 104, "column": null}}, "54": {"start": {"line": 107, "column": 8}, "end": {"line": 107, "column": null}}, "55": {"start": {"line": 108, "column": 8}, "end": {"line": 108, "column": null}}, "56": {"start": {"line": 109, "column": 8}, "end": {"line": 109, "column": null}}, "57": {"start": {"line": 113, "column": 4}, "end": {"line": 115, "column": null}}, "58": {"start": {"line": 114, "column": 6}, "end": {"line": 114, "column": null}}, "59": {"start": {"line": 155, "column": 14}, "end": {"line": 155, "column": 29}}}, "fnMap": {"0": {"name": "useReducedMotion", "decl": {"start": {"line": 11, "column": 9}, "end": {"line": 11, "column": null}}, "loc": {"start": {"line": 11, "column": 9}, "end": {"line": 30, "column": null}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 14, "column": 12}, "end": {"line": 14, "column": null}}, "loc": {"start": {"line": 14, "column": 12}, "end": {"line": 27, "column": 5}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 21, "column": 25}, "end": {"line": 21, "column": 26}}, "loc": {"start": {"line": 21, "column": 26}, "end": {"line": 23, "column": null}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 26, "column": 11}, "end": {"line": 26, "column": 17}}, "loc": {"start": {"line": 26, "column": 17}, "end": {"line": 26, "column": null}}}, "4": {"name": "TypingTerminal", "decl": {"start": {"line": 54, "column": 16}, "end": {"line": 54, "column": 31}}, "loc": {"start": {"line": 60, "column": 22}, "end": {"line": 174, "column": null}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 61, "column": 57}, "end": {"line": 61, "column": 63}}, "loc": {"start": {"line": 61, "column": 63}, "end": {"line": 61, "column": null}}}, "6": {"name": "(anonymous_10)", "decl": {"start": {"line": 69, "column": 29}, "end": {"line": 69, "column": 35}}, "loc": {"start": {"line": 69, "column": 35}, "end": {"line": 69, "column": 69}}}, "7": {"name": "(anonymous_11)", "decl": {"start": {"line": 72, "column": 12}, "end": {"line": 72, "column": null}}, "loc": {"start": {"line": 72, "column": 12}, "end": {"line": 116, "column": 5}}}, "8": {"name": "(anonymous_12)", "decl": {"start": {"line": 74, "column": 18}, "end": {"line": 74, "column": 19}}, "loc": {"start": {"line": 74, "column": 34}, "end": {"line": 74, "column": null}}}, "9": {"name": "(anonymous_13)", "decl": {"start": {"line": 74, "column": 46}, "end": {"line": 74, "column": 51}}, "loc": {"start": {"line": 74, "column": 51}, "end": {"line": 74, "column": null}}}, "10": {"name": "(anonymous_14)", "decl": {"start": {"line": 76, "column": 6}, "end": {"line": 76, "column": null}}, "loc": {"start": {"line": 76, "column": 6}, "end": {"line": 111, "column": null}}}, "11": {"name": "(anonymous_15)", "decl": {"start": {"line": 78, "column": 17}, "end": {"line": 78, "column": 23}}, "loc": {"start": {"line": 78, "column": 23}, "end": {"line": 78, "column": 53}}}, "12": {"name": "(anonymous_16)", "decl": {"start": {"line": 97, "column": 26}, "end": {"line": 97, "column": null}}, "loc": {"start": {"line": 97, "column": 26}, "end": {"line": 101, "column": null}}}, "13": {"name": "(anonymous_17)", "decl": {"start": {"line": 113, "column": 11}, "end": {"line": 113, "column": null}}, "loc": {"start": {"line": 113, "column": 11}, "end": {"line": 115, "column": null}}}, "14": {"name": "(anonymous_18)", "decl": {"start": {"line": 154, "column": 22}, "end": {"line": 154, "column": 23}}, "loc": {"start": {"line": 155, "column": 14}, "end": {"line": 155, "column": 29}}}}, "branchMap": {"0": {"loc": {"start": {"line": 16, "column": 4}, "end": {"line": 16, "column": null}}, "type": "if", "locations": [{"start": {"line": 16, "column": 4}, "end": {"line": 16, "column": null}}]}, "1": {"loc": {"start": {"line": 56, "column": 2}, "end": {"line": 56, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 56, "column": 14}, "end": {"line": 56, "column": 16}}]}, "2": {"loc": {"start": {"line": 58, "column": 2}, "end": {"line": 58, "column": 27}}, "type": "default-arg", "locations": [{"start": {"line": 58, "column": 22}, "end": {"line": 58, "column": 27}}]}, "3": {"loc": {"start": {"line": 59, "column": 2}, "end": {"line": 59, "column": 50}}, "type": "default-arg", "locations": [{"start": {"line": 59, "column": 16}, "end": {"line": 59, "column": 50}}]}, "4": {"loc": {"start": {"line": 81, "column": 6}, "end": {"line": 85, "column": null}}, "type": "if", "locations": [{"start": {"line": 81, "column": 6}, "end": {"line": 85, "column": null}}]}, "5": {"loc": {"start": {"line": 91, "column": 10}, "end": {"line": 91, "column": null}}, "type": "if", "locations": [{"start": {"line": 91, "column": 10}, "end": {"line": 91, "column": null}}]}, "6": {"loc": {"start": {"line": 94, "column": 10}, "end": {"line": 94, "column": 30}}, "type": "if", "locations": [{"start": {"line": 94, "column": 10}, "end": {"line": 94, "column": 30}}]}, "7": {"loc": {"start": {"line": 96, "column": 12}, "end": {"line": 96, "column": null}}, "type": "if", "locations": [{"start": {"line": 96, "column": 12}, "end": {"line": 96, "column": null}}]}, "8": {"loc": {"start": {"line": 127, "column": 9}, "end": {"line": 138, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 128, "column": 10}, "end": {"line": 135, "column": null}}, {"start": {"line": 138, "column": 10}, "end": {"line": 138, "column": null}}]}, "9": {"loc": {"start": {"line": 128, "column": 10}, "end": {"line": 135, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 130, "column": 12}, "end": {"line": 130, "column": 50}}, {"start": {"line": 133, "column": 12}, "end": {"line": 135, "column": null}}]}, "10": {"loc": {"start": {"line": 133, "column": 12}, "end": {"line": 135, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 134, "column": 14}, "end": {"line": 134, "column": 55}}, {"start": {"line": 135, "column": 14}, "end": {"line": 135, "column": null}}]}, "11": {"loc": {"start": {"line": 133, "column": 12}, "end": {"line": 133, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 133, "column": 12}, "end": {"line": 133, "column": 31}}, {"start": {"line": 133, "column": 31}, "end": {"line": 133, "column": 56}}]}, "12": {"loc": {"start": {"line": 134, "column": 25}, "end": {"line": 134, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 134, "column": 25}, "end": {"line": 134, "column": 47}}, {"start": {"line": 134, "column": 51}, "end": {"line": 134, "column": 55}}]}, "13": {"loc": {"start": {"line": 148, "column": 11}, "end": {"line": 155, "column": 29}}, "type": "cond-expr", "locations": [{"start": {"line": 149, "column": 12}, "end": {"line": 154, "column": 18}}, {"start": {"line": 154, "column": 12}, "end": {"line": 155, "column": 29}}]}, "14": {"loc": {"start": {"line": 157, "column": 18}, "end": {"line": 159, "column": 19}}, "type": "cond-expr", "locations": [{"start": {"line": 157, "column": 18}, "end": {"line": 159, "column": 19}}]}, "15": {"loc": {"start": {"line": 161, "column": 17}, "end": {"line": 163, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 162, "column": 18}, "end": {"line": 163, "column": null}}, {"start": {"line": 163, "column": 20}, "end": {"line": 163, "column": null}}]}, "16": {"loc": {"start": {"line": 161, "column": 17}, "end": {"line": 161, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 161, "column": 17}, "end": {"line": 161, "column": 42}}, {"start": {"line": 161, "column": 42}, "end": {"line": 161, "column": null}}]}, "17": {"loc": {"start": {"line": 164, "column": 17}, "end": {"line": 166, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 165, "column": 18}, "end": {"line": 166, "column": null}}, {"start": {"line": 166, "column": 20}, "end": {"line": 166, "column": null}}]}, "18": {"loc": {"start": {"line": 164, "column": 17}, "end": {"line": 164, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 164, "column": 17}, "end": {"line": 164, "column": 39}}, {"start": {"line": 164, "column": 39}, "end": {"line": 164, "column": null}}]}}, "s": {"0": 16, "1": 1, "2": 1, "3": 1, "4": 96, "5": 96, "6": 15, "7": 0, "8": 15, "9": 15, "10": 15, "11": 1, "12": 15, "13": 15, "14": 15, "15": 96, "16": 96, "17": 15, "18": 96, "19": 96, "20": 96, "21": 96, "22": 16, "23": 96, "24": 18, "25": 18, "26": 71, "27": 71, "28": 18, "29": 18, "30": 16, "31": 18, "32": 2, "33": 2, "34": 16, "35": 16, "36": 16, "37": 16, "38": 15, "39": 0, "40": 15, "41": 15, "42": 15, "43": 0, "44": 15, "45": 15, "46": 82, "47": 12, "48": 70, "49": 69, "50": 69, "51": 69, "52": 70, "53": 0, "54": 1, "55": 1, "56": 1, "57": 18, "58": 18, "59": 27}, "f": {"0": 96, "1": 15, "2": 1, "3": 15, "4": 96, "5": 15, "6": 16, "7": 18, "8": 71, "9": 71, "10": 18, "11": 16, "12": 69, "13": 18, "14": 27}, "b": {"0": [0], "1": [96], "2": [84], "3": [92], "4": [2], "5": [0], "6": [0], "7": [12], "8": [9, 87], "9": [3, 6], "10": [6, 0], "11": [6, 6], "12": [6, 0], "13": [87, 9], "14": [1], "15": [0, 27], "16": [27, 1], "17": [6, 21], "18": [27, 7]}}, "/Users/<USER>/Downloads/Code/go42/components/ui/button.tsx": {"path": "/Users/<USER>/Downloads/Code/go42/components/ui/button.tsx", "statementMap": {"0": {"start": {"line": 56, "column": 9}, "end": {"line": 56, "column": 15}}, "1": {"start": {"line": 56, "column": 17}, "end": {"line": 56, "column": 31}}, "2": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": null}}, "3": {"start": {"line": 2, "column": 21}, "end": {"line": 2, "column": null}}, "4": {"start": {"line": 3, "column": 39}, "end": {"line": 3, "column": null}}, "5": {"start": {"line": 5, "column": 19}, "end": {"line": 5, "column": null}}, "6": {"start": {"line": 7, "column": 23}, "end": {"line": 32, "column": null}}, "7": {"start": {"line": 45, "column": 15}, "end": {"line": 45, "column": null}}}, "fnMap": {"0": {"name": "<PERSON><PERSON>", "decl": {"start": {"line": 35, "column": 9}, "end": {"line": 35, "column": 16}}, "loc": {"start": {"line": 44, "column": 3}, "end": {"line": 54, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 39, "column": 2}, "end": {"line": 39, "column": 17}}, "type": "default-arg", "locations": [{"start": {"line": 39, "column": 12}, "end": {"line": 39, "column": 17}}]}, "1": {"loc": {"start": {"line": 45, "column": 15}, "end": {"line": 45, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 45, "column": 25}, "end": {"line": 45, "column": 29}}, {"start": {"line": 45, "column": 32}, "end": {"line": 45, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "f": {"0": 0}, "b": {"0": [0], "1": [0, 0]}}, "/Users/<USER>/Downloads/Code/go42/components/ui/card.tsx": {"path": "/Users/<USER>/Downloads/Code/go42/components/ui/card.tsx", "statementMap": {"0": {"start": {"line": 75, "column": 9}, "end": {"line": 75, "column": 13}}, "1": {"start": {"line": 75, "column": 50}, "end": {"line": 75, "column": 60}}, "2": {"start": {"line": 75, "column": 79}, "end": {"line": 75, "column": 90}}, "3": {"start": {"line": 75, "column": 62}, "end": {"line": 75, "column": 77}}, "4": {"start": {"line": 75, "column": 27}, "end": {"line": 75, "column": 37}}, "5": {"start": {"line": 75, "column": 15}, "end": {"line": 75, "column": 25}}, "6": {"start": {"line": 75, "column": 39}, "end": {"line": 75, "column": 48}}, "7": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": null}}, "8": {"start": {"line": 3, "column": 19}, "end": {"line": 3, "column": null}}}, "fnMap": {"0": {"name": "Card", "decl": {"start": {"line": 5, "column": 9}, "end": {"line": 5, "column": 14}}, "loc": {"start": {"line": 5, "column": 66}, "end": {"line": 16, "column": null}}}, "1": {"name": "<PERSON><PERSON><PERSON><PERSON>", "decl": {"start": {"line": 18, "column": 9}, "end": {"line": 18, "column": 20}}, "loc": {"start": {"line": 18, "column": 72}, "end": {"line": 29, "column": null}}}, "2": {"name": "CardTitle", "decl": {"start": {"line": 31, "column": 9}, "end": {"line": 31, "column": 19}}, "loc": {"start": {"line": 31, "column": 71}, "end": {"line": 39, "column": null}}}, "3": {"name": "CardDescription", "decl": {"start": {"line": 41, "column": 9}, "end": {"line": 41, "column": 25}}, "loc": {"start": {"line": 41, "column": 77}, "end": {"line": 49, "column": null}}}, "4": {"name": "CardAction", "decl": {"start": {"line": 51, "column": 9}, "end": {"line": 51, "column": 20}}, "loc": {"start": {"line": 51, "column": 72}, "end": {"line": 59, "column": null}}}, "5": {"name": "<PERSON><PERSON><PERSON><PERSON>", "decl": {"start": {"line": 61, "column": 9}, "end": {"line": 61, "column": 21}}, "loc": {"start": {"line": 61, "column": 73}, "end": {"line": 63, "column": null}}}, "6": {"name": "<PERSON><PERSON><PERSON>er", "decl": {"start": {"line": 65, "column": 9}, "end": {"line": 65, "column": 20}}, "loc": {"start": {"line": 65, "column": 72}, "end": {"line": 73, "column": null}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "b": {}}, "/Users/<USER>/Downloads/Code/go42/components/ui/loading-spinner.tsx": {"path": "/Users/<USER>/Downloads/Code/go42/components/ui/loading-spinner.tsx", "statementMap": {"0": {"start": {"line": 44, "column": 16}, "end": {"line": 44, "column": 30}}, "1": {"start": {"line": 70, "column": 16}, "end": {"line": 70, "column": 31}}, "2": {"start": {"line": 9, "column": 16}, "end": {"line": 9, "column": 30}}, "3": {"start": {"line": 1, "column": 18}, "end": {"line": 1, "column": null}}, "4": {"start": {"line": 14, "column": 22}, "end": {"line": 18, "column": null}}, "5": {"start": {"line": 20, "column": 23}, "end": {"line": 24, "column": null}}, "6": {"start": {"line": 74, "column": 8}, "end": {"line": 75, "column": null}}}, "fnMap": {"0": {"name": "LoadingSpinner", "decl": {"start": {"line": 9, "column": 16}, "end": {"line": 9, "column": 30}}, "loc": {"start": {"line": 13, "column": 22}, "end": {"line": 35, "column": null}}}, "1": {"name": "LoadingOverlay", "decl": {"start": {"line": 44, "column": 16}, "end": {"line": 44, "column": 30}}, "loc": {"start": {"line": 49, "column": 22}, "end": {"line": 63, "column": null}}}, "2": {"name": "LoadingSkeleton", "decl": {"start": {"line": 70, "column": 16}, "end": {"line": 70, "column": 31}}, "loc": {"start": {"line": 70, "column": 83}, "end": {"line": 83, "column": null}}}, "3": {"name": "(anonymous_8)", "decl": {"start": {"line": 73, "column": 41}, "end": {"line": 73, "column": 42}}, "loc": {"start": {"line": 74, "column": 8}, "end": {"line": 75, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 10, "column": 2}, "end": {"line": 10, "column": 13}}, "type": "default-arg", "locations": [{"start": {"line": 10, "column": 9}, "end": {"line": 10, "column": 13}}]}, "1": {"loc": {"start": {"line": 11, "column": 2}, "end": {"line": 11, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 11, "column": 14}, "end": {"line": 11, "column": 16}}]}, "2": {"loc": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": 19}}, "type": "default-arg", "locations": [{"start": {"line": 12, "column": 10}, "end": {"line": 12, "column": 19}}]}, "3": {"loc": {"start": {"line": 47, "column": 2}, "end": {"line": 47, "column": 28}}, "type": "default-arg", "locations": [{"start": {"line": 47, "column": 16}, "end": {"line": 47, "column": 28}}]}, "4": {"loc": {"start": {"line": 48, "column": 2}, "end": {"line": 48, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 48, "column": 14}, "end": {"line": 48, "column": 16}}]}, "5": {"loc": {"start": {"line": 53, "column": 7}, "end": {"line": 53, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 53, "column": 7}, "end": {"line": 53, "column": null}}]}, "6": {"loc": {"start": {"line": 70, "column": 34}, "end": {"line": 70, "column": 48}}, "type": "default-arg", "locations": [{"start": {"line": 70, "column": 46}, "end": {"line": 70, "column": 48}}]}, "7": {"loc": {"start": {"line": 70, "column": 50}, "end": {"line": 70, "column": 59}}, "type": "default-arg", "locations": [{"start": {"line": 70, "column": 58}, "end": {"line": 70, "column": 59}}]}, "8": {"loc": {"start": {"line": 76, "column": 45}, "end": {"line": 76, "column": 69}}, "type": "cond-expr", "locations": [{"start": {"line": 76, "column": 57}, "end": {"line": 76, "column": 66}}, {"start": {"line": 76, "column": 66}, "end": {"line": 76, "column": 69}}]}, "9": {"loc": {"start": {"line": 77, "column": 12}, "end": {"line": 77, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 77, "column": 47}, "end": {"line": 77, "column": 57}}, {"start": {"line": 77, "column": 57}, "end": {"line": 77, "column": null}}]}, "10": {"loc": {"start": {"line": 77, "column": 12}, "end": {"line": 77, "column": 47}}, "type": "binary-expr", "locations": [{"start": {"line": 77, "column": 12}, "end": {"line": 77, "column": 35}}, {"start": {"line": 77, "column": 35}, "end": {"line": 77, "column": 47}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0], "4": [0], "5": [0], "6": [0], "7": [0], "8": [0, 0], "9": [0, 0], "10": [0, 0]}}, "/Users/<USER>/Downloads/Code/go42/hooks/spotlight.tsx": {"path": "/Users/<USER>/Downloads/Code/go42/hooks/spotlight.tsx", "statementMap": {"0": {"start": {"line": 46, "column": 16}, "end": {"line": 46, "column": 33}}, "1": {"start": {"line": 419, "column": 9}, "end": {"line": 419, "column": null}}, "2": {"start": {"line": 58, "column": 16}, "end": {"line": 58, "column": 36}}, "3": {"start": {"line": 261, "column": 16}, "end": {"line": 261, "column": 28}}, "4": {"start": {"line": 51, "column": 16}, "end": {"line": 51, "column": 35}}, "5": {"start": {"line": 1, "column": 68}, "end": {"line": 1, "column": null}}, "6": {"start": {"line": 2, "column": 53}, "end": {"line": 2, "column": null}}, "7": {"start": {"line": 3, "column": 48}, "end": {"line": 3, "column": null}}, "8": {"start": {"line": 19, "column": 25}, "end": {"line": 19, "column": null}}, "9": {"start": {"line": 28, "column": 20}, "end": {"line": 28, "column": null}}, "10": {"start": {"line": 29, "column": 20}, "end": {"line": 29, "column": null}}, "11": {"start": {"line": 31, "column": 2}, "end": {"line": 41, "column": null}}, "12": {"start": {"line": 32, "column": 19}, "end": {"line": 35, "column": null}}, "13": {"start": {"line": 33, "column": 6}, "end": {"line": 33, "column": null}}, "14": {"start": {"line": 34, "column": 6}, "end": {"line": 34, "column": null}}, "15": {"start": {"line": 37, "column": 4}, "end": {"line": 37, "column": null}}, "16": {"start": {"line": 38, "column": 4}, "end": {"line": 40, "column": null}}, "17": {"start": {"line": 39, "column": 6}, "end": {"line": 39, "column": null}}, "18": {"start": {"line": 43, "column": 2}, "end": {"line": 43, "column": null}}, "19": {"start": {"line": 47, "column": 16}, "end": {"line": 47, "column": null}}, "20": {"start": {"line": 52, "column": 14}, "end": {"line": 52, "column": null}}, "21": {"start": {"line": 53, "column": 2}, "end": {"line": 53, "column": null}}, "22": {"start": {"line": 53, "column": 12}, "end": {"line": 53, "column": null}}, "23": {"start": {"line": 54, "column": 2}, "end": {"line": 54, "column": null}}, "24": {"start": {"line": 63, "column": 71}, "end": {"line": 63, "column": null}}, "25": {"start": {"line": 64, "column": 22}, "end": {"line": 64, "column": null}}, "26": {"start": {"line": 65, "column": 21}, "end": {"line": 65, "column": null}}, "27": {"start": {"line": 66, "column": 28}, "end": {"line": 66, "column": null}}, "28": {"start": {"line": 67, "column": 24}, "end": {"line": 67, "column": null}}, "29": {"start": {"line": 69, "column": 22}, "end": {"line": 73, "column": null}}, "30": {"start": {"line": 70, "column": 17}, "end": {"line": 70, "column": 32}}, "31": {"start": {"line": 71, "column": 4}, "end": {"line": 71, "column": null}}, "32": {"start": {"line": 71, "column": 15}, "end": {"line": 71, "column": null}}, "33": {"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": null}}, "34": {"start": {"line": 75, "column": 25}, "end": {"line": 81, "column": null}}, "35": {"start": {"line": 76, "column": 21}, "end": {"line": 76, "column": null}}, "36": {"start": {"line": 77, "column": 4}, "end": {"line": 80, "column": null}}, "37": {"start": {"line": 78, "column": 16}, "end": {"line": 78, "column": null}}, "38": {"start": {"line": 79, "column": 6}, "end": {"line": 79, "column": null}}, "39": {"start": {"line": 83, "column": 24}, "end": {"line": 211, "column": null}}, "40": {"start": {"line": 84, "column": 17}, "end": {"line": 84, "column": 32}}, "41": {"start": {"line": 85, "column": 21}, "end": {"line": 85, "column": null}}, "42": {"start": {"line": 86, "column": 4}, "end": {"line": 86, "column": null}}, "43": {"start": {"line": 86, "column": 28}, "end": {"line": 86, "column": null}}, "44": {"start": {"line": 87, "column": 4}, "end": {"line": 87, "column": null}}, "45": {"start": {"line": 89, "column": 21}, "end": {"line": 89, "column": null}}, "46": {"start": {"line": 91, "column": 20}, "end": {"line": 91, "column": null}}, "47": {"start": {"line": 92, "column": 16}, "end": {"line": 92, "column": 19}}, "48": {"start": {"line": 94, "column": 98}, "end": {"line": 94, "column": 100}}, "49": {"start": {"line": 95, "column": 20}, "end": {"line": 98, "column": null}}, "50": {"start": {"line": 96, "column": 6}, "end": {"line": 96, "column": null}}, "51": {"start": {"line": 96, "column": 14}, "end": {"line": 96, "column": null}}, "52": {"start": {"line": 97, "column": 6}, "end": {"line": 97, "column": null}}, "53": {"start": {"line": 99, "column": 4}, "end": {"line": 99, "column": null}}, "54": {"start": {"line": 99, "column": 32}, "end": {"line": 99, "column": null}}, "55": {"start": {"line": 101, "column": 30}, "end": {"line": 108, "column": null}}, "56": {"start": {"line": 102, "column": 17}, "end": {"line": 107, "column": null}}, "57": {"start": {"line": 108, "column": 19}, "end": {"line": 108, "column": 55}}, "58": {"start": {"line": 111, "column": 29}, "end": {"line": 117, "column": null}}, "59": {"start": {"line": 111, "column": 49}, "end": {"line": 117, "column": null}}, "60": {"start": {"line": 119, "column": 17}, "end": {"line": 119, "column": null}}, "61": {"start": {"line": 120, "column": 17}, "end": {"line": 120, "column": null}}, "62": {"start": {"line": 121, "column": 18}, "end": {"line": 121, "column": null}}, "63": {"start": {"line": 122, "column": 18}, "end": {"line": 122, "column": null}}, "64": {"start": {"line": 124, "column": 34}, "end": {"line": 125, "column": null}}, "65": {"start": {"line": 125, "column": 6}, "end": {"line": 125, "column": null}}, "66": {"start": {"line": 128, "column": 56}, "end": {"line": 128, "column": 58}}, "67": {"start": {"line": 129, "column": 4}, "end": {"line": 129, "column": null}}, "68": {"start": {"line": 129, "column": 17}, "end": {"line": 129, "column": 20}}, "69": {"start": {"line": 129, "column": 35}, "end": {"line": 129, "column": null}}, "70": {"start": {"line": 129, "column": 48}, "end": {"line": 129, "column": 51}}, "71": {"start": {"line": 129, "column": 66}, "end": {"line": 129, "column": null}}, "72": {"start": {"line": 130, "column": 4}, "end": {"line": 137, "column": null}}, "73": {"start": {"line": 130, "column": 17}, "end": {"line": 130, "column": 40}}, "74": {"start": {"line": 131, "column": 16}, "end": {"line": 131, "column": null}}, "75": {"start": {"line": 132, "column": 25}, "end": {"line": 132, "column": 38}}, "76": {"start": {"line": 133, "column": 25}, "end": {"line": 133, "column": 38}}, "77": {"start": {"line": 134, "column": 6}, "end": {"line": 136, "column": null}}, "78": {"start": {"line": 135, "column": 9}, "end": {"line": 135, "column": null}}, "79": {"start": {"line": 139, "column": 21}, "end": {"line": 196, "column": null}}, "80": {"start": {"line": 140, "column": 20}, "end": {"line": 140, "column": null}}, "81": {"start": {"line": 141, "column": 20}, "end": {"line": 141, "column": null}}, "82": {"start": {"line": 142, "column": 20}, "end": {"line": 142, "column": null}}, "83": {"start": {"line": 143, "column": 6}, "end": {"line": 150, "column": null}}, "84": {"start": {"line": 143, "column": 19}, "end": {"line": 143, "column": 37}}, "85": {"start": {"line": 144, "column": 18}, "end": {"line": 144, "column": null}}, "86": {"start": {"line": 145, "column": 23}, "end": {"line": 145, "column": 31}}, "87": {"start": {"line": 146, "column": 23}, "end": {"line": 146, "column": 31}}, "88": {"start": {"line": 147, "column": 8}, "end": {"line": 149, "column": null}}, "89": {"start": {"line": 148, "column": 11}, "end": {"line": 148, "column": null}}, "90": {"start": {"line": 151, "column": 6}, "end": {"line": 194, "column": null}}, "91": {"start": {"line": 152, "column": 8}, "end": {"line": 152, "column": null}}, "92": {"start": {"line": 152, "column": 50}, "end": {"line": 152, "column": null}}, "93": {"start": {"line": 153, "column": 17}, "end": {"line": 153, "column": null}}, "94": {"start": {"line": 154, "column": 8}, "end": {"line": 159, "column": null}}, "95": {"start": {"line": 154, "column": 22}, "end": {"line": 154, "column": 25}}, "96": {"start": {"line": 155, "column": 10}, "end": {"line": 159, "column": null}}, "97": {"start": {"line": 155, "column": 24}, "end": {"line": 155, "column": 27}}, "98": {"start": {"line": 156, "column": 12}, "end": {"line": 159, "column": null}}, "99": {"start": {"line": 157, "column": 14}, "end": {"line": 157, "column": null}}, "100": {"start": {"line": 158, "column": 14}, "end": {"line": 158, "column": null}}, "101": {"start": {"line": 160, "column": 8}, "end": {"line": 160, "column": null}}, "102": {"start": {"line": 160, "column": 17}, "end": {"line": 160, "column": null}}, "103": {"start": {"line": 161, "column": 19}, "end": {"line": 161, "column": null}}, "104": {"start": {"line": 162, "column": 19}, "end": {"line": 162, "column": null}}, "105": {"start": {"line": 163, "column": 26}, "end": {"line": 163, "column": null}}, "106": {"start": {"line": 164, "column": 27}, "end": {"line": 164, "column": null}}, "107": {"start": {"line": 165, "column": 21}, "end": {"line": 165, "column": null}}, "108": {"start": {"line": 166, "column": 21}, "end": {"line": 166, "column": null}}, "109": {"start": {"line": 167, "column": 8}, "end": {"line": 167, "column": null}}, "110": {"start": {"line": 168, "column": 8}, "end": {"line": 168, "column": null}}, "111": {"start": {"line": 170, "column": 23}, "end": {"line": 170, "column": null}}, "112": {"start": {"line": 171, "column": 26}, "end": {"line": 171, "column": null}}, "113": {"start": {"line": 172, "column": 35}, "end": {"line": 185, "column": null}}, "114": {"start": {"line": 173, "column": 27}, "end": {"line": 178, "column": null}}, "115": {"start": {"line": 179, "column": 10}, "end": {"line": 183, "column": 42}}, "116": {"start": {"line": 186, "column": 8}, "end": {"line": 186, "column": null}}, "117": {"start": {"line": 186, "column": 32}, "end": {"line": 186, "column": null}}, "118": {"start": {"line": 188, "column": 8}, "end": {"line": 192, "column": null}}, "119": {"start": {"line": 188, "column": 22}, "end": {"line": 188, "column": 25}}, "120": {"start": {"line": 189, "column": 10}, "end": {"line": 192, "column": null}}, "121": {"start": {"line": 189, "column": 24}, "end": {"line": 189, "column": 27}}, "122": {"start": {"line": 190, "column": 24}, "end": {"line": 190, "column": 36}}, "123": {"start": {"line": 191, "column": 12}, "end": {"line": 191, "column": null}}, "124": {"start": {"line": 191, "column": 21}, "end": {"line": 191, "column": null}}, "125": {"start": {"line": 193, "column": 8}, "end": {"line": 193, "column": null}}, "126": {"start": {"line": 195, "column": 6}, "end": {"line": 195, "column": null}}, "127": {"start": {"line": 198, "column": 4}, "end": {"line": 204, "column": null}}, "128": {"start": {"line": 199, "column": 6}, "end": {"line": 203, "column": null}}, "129": {"start": {"line": 200, "column": 8}, "end": {"line": 200, "column": null}}, "130": {"start": {"line": 201, "column": 8}, "end": {"line": 201, "column": null}}, "131": {"start": {"line": 202, "column": 8}, "end": {"line": 202, "column": null}}, "132": {"start": {"line": 206, "column": 4}, "end": {"line": 210, "column": null}}, "133": {"start": {"line": 207, "column": 6}, "end": {"line": 207, "column": null}}, "134": {"start": {"line": 208, "column": 6}, "end": {"line": 208, "column": null}}, "135": {"start": {"line": 209, "column": 6}, "end": {"line": 209, "column": null}}, "136": {"start": {"line": 213, "column": 31}, "end": {"line": 222, "column": null}}, "137": {"start": {"line": 214, "column": 17}, "end": {"line": 214, "column": 32}}, "138": {"start": {"line": 215, "column": 4}, "end": {"line": 215, "column": null}}, "139": {"start": {"line": 215, "column": 15}, "end": {"line": 215, "column": null}}, "140": {"start": {"line": 216, "column": 4}, "end": {"line": 216, "column": null}}, "141": {"start": {"line": 217, "column": 4}, "end": {"line": 220, "column": null}}, "142": {"start": {"line": 218, "column": 6}, "end": {"line": 218, "column": null}}, "143": {"start": {"line": 219, "column": 6}, "end": {"line": 219, "column": null}}, "144": {"start": {"line": 221, "column": 4}, "end": {"line": 221, "column": null}}, "145": {"start": {"line": 224, "column": 22}, "end": {"line": 232, "column": null}}, "146": {"start": {"line": 225, "column": 17}, "end": {"line": 225, "column": 32}}, "147": {"start": {"line": 226, "column": 4}, "end": {"line": 226, "column": null}}, "148": {"start": {"line": 226, "column": 56}, "end": {"line": 226, "column": null}}, "149": {"start": {"line": 227, "column": 15}, "end": {"line": 229, "column": null}}, "150": {"start": {"line": 228, "column": 6}, "end": {"line": 228, "column": null}}, "151": {"start": {"line": 230, "column": 4}, "end": {"line": 230, "column": null}}, "152": {"start": {"line": 231, "column": 4}, "end": {"line": 231, "column": null}}, "153": {"start": {"line": 234, "column": 29}, "end": {"line": 237, "column": null}}, "154": {"start": {"line": 235, "column": 4}, "end": {"line": 235, "column": null}}, "155": {"start": {"line": 236, "column": 4}, "end": {"line": 236, "column": null}}, "156": {"start": {"line": 239, "column": 2}, "end": {"line": 239, "column": null}}, "157": {"start": {"line": 276, "column": 6}, "end": {"line": 276, "column": null}}, "158": {"start": {"line": 277, "column": 35}, "end": {"line": 277, "column": null}}, "159": {"start": {"line": 280, "column": 21}, "end": {"line": 280, "column": null}}, "160": {"start": {"line": 281, "column": 21}, "end": {"line": 281, "column": null}}, "161": {"start": {"line": 282, "column": 27}, "end": {"line": 282, "column": null}}, "162": {"start": {"line": 285, "column": 4}, "end": {"line": 285, "column": null}}, "163": {"start": {"line": 287, "column": 2}, "end": {"line": 404, "column": null}}, "164": {"start": {"line": 288, "column": 17}, "end": {"line": 288, "column": 32}}, "165": {"start": {"line": 289, "column": 17}, "end": {"line": 289, "column": 32}}, "166": {"start": {"line": 290, "column": 4}, "end": {"line": 290, "column": null}}, "167": {"start": {"line": 290, "column": 24}, "end": {"line": 290, "column": null}}, "168": {"start": {"line": 292, "column": 16}, "end": {"line": 292, "column": null}}, "169": {"start": {"line": 293, "column": 22}, "end": {"line": 293, "column": null}}, "170": {"start": {"line": 294, "column": 22}, "end": {"line": 294, "column": null}}, "171": {"start": {"line": 296, "column": 19}, "end": {"line": 345, "column": null}}, "172": {"start": {"line": 297, "column": 6}, "end": {"line": 297, "column": null}}, "173": {"start": {"line": 298, "column": 23}, "end": {"line": 298, "column": 42}}, "174": {"start": {"line": 299, "column": 6}, "end": {"line": 299, "column": null}}, "175": {"start": {"line": 299, "column": 21}, "end": {"line": 299, "column": null}}, "176": {"start": {"line": 301, "column": 21}, "end": {"line": 301, "column": 38}}, "177": {"start": {"line": 302, "column": 21}, "end": {"line": 302, "column": 38}}, "178": {"start": {"line": 304, "column": 8}, "end": {"line": 307, "column": 33}}, "179": {"start": {"line": 309, "column": 6}, "end": {"line": 319, "column": null}}, "180": {"start": {"line": 311, "column": 8}, "end": {"line": 311, "column": null}}, "181": {"start": {"line": 312, "column": 8}, "end": {"line": 312, "column": null}}, "182": {"start": {"line": 313, "column": 8}, "end": {"line": 313, "column": null}}, "183": {"start": {"line": 314, "column": 8}, "end": {"line": 317, "column": null}}, "184": {"start": {"line": 316, "column": 10}, "end": {"line": 316, "column": null}}, "185": {"start": {"line": 318, "column": 8}, "end": {"line": 318, "column": null}}, "186": {"start": {"line": 321, "column": 16}, "end": {"line": 321, "column": null}}, "187": {"start": {"line": 322, "column": 16}, "end": {"line": 322, "column": null}}, "188": {"start": {"line": 324, "column": 6}, "end": {"line": 324, "column": null}}, "189": {"start": {"line": 325, "column": 6}, "end": {"line": 325, "column": null}}, "190": {"start": {"line": 326, "column": 6}, "end": {"line": 326, "column": null}}, "191": {"start": {"line": 328, "column": 6}, "end": {"line": 344, "column": null}}, "192": {"start": {"line": 329, "column": 19}, "end": {"line": 329, "column": null}}, "193": {"start": {"line": 330, "column": 19}, "end": {"line": 330, "column": null}}, "194": {"start": {"line": 331, "column": 18}, "end": {"line": 331, "column": null}}, "195": {"start": {"line": 333, "column": 8}, "end": {"line": 338, "column": null}}, "196": {"start": {"line": 333, "column": 30}, "end": {"line": 333, "column": null}}, "197": {"start": {"line": 334, "column": 13}, "end": {"line": 338, "column": null}}, "198": {"start": {"line": 334, "column": 35}, "end": {"line": 334, "column": null}}, "199": {"start": {"line": 336, "column": 20}, "end": {"line": 336, "column": null}}, "200": {"start": {"line": 337, "column": 10}, "end": {"line": 337, "column": null}}, "201": {"start": {"line": 339, "column": 27}, "end": {"line": 339, "column": null}}, "202": {"start": {"line": 340, "column": 27}, "end": {"line": 340, "column": null}}, "203": {"start": {"line": 341, "column": 24}, "end": {"line": 341, "column": null}}, "204": {"start": {"line": 343, "column": 8}, "end": {"line": 343, "column": null}}, "205": {"start": {"line": 347, "column": 26}, "end": {"line": 352, "column": null}}, "206": {"start": {"line": 348, "column": 6}, "end": {"line": 351, "column": null}}, "207": {"start": {"line": 349, "column": 8}, "end": {"line": 349, "column": null}}, "208": {"start": {"line": 350, "column": 8}, "end": {"line": 350, "column": null}}, "209": {"start": {"line": 354, "column": 23}, "end": {"line": 361, "column": null}}, "210": {"start": {"line": 355, "column": 6}, "end": {"line": 360, "column": null}}, "211": {"start": {"line": 356, "column": 8}, "end": {"line": 359, "column": null}}, "212": {"start": {"line": 358, "column": 10}, "end": {"line": 358, "column": null}}, "213": {"start": {"line": 364, "column": 4}, "end": {"line": 364, "column": null}}, "214": {"start": {"line": 365, "column": 4}, "end": {"line": 365, "column": null}}, "215": {"start": {"line": 367, "column": 21}, "end": {"line": 370, "column": null}}, "216": {"start": {"line": 369, "column": 6}, "end": {"line": 369, "column": null}}, "217": {"start": {"line": 371, "column": 21}, "end": {"line": 374, "column": null}}, "218": {"start": {"line": 372, "column": 6}, "end": {"line": 372, "column": null}}, "219": {"start": {"line": 373, "column": 6}, "end": {"line": 373, "column": null}}, "220": {"start": {"line": 376, "column": 4}, "end": {"line": 376, "column": null}}, "221": {"start": {"line": 377, "column": 4}, "end": {"line": 377, "column": null}}, "222": {"start": {"line": 378, "column": 4}, "end": {"line": 378, "column": null}}, "223": {"start": {"line": 380, "column": 19}, "end": {"line": 380, "column": null}}, "224": {"start": {"line": 380, "column": 25}, "end": {"line": 380, "column": null}}, "225": {"start": {"line": 381, "column": 4}, "end": {"line": 381, "column": null}}, "226": {"start": {"line": 383, "column": 4}, "end": {"line": 390, "column": null}}, "227": {"start": {"line": 384, "column": 6}, "end": {"line": 384, "column": null}}, "228": {"start": {"line": 385, "column": 6}, "end": {"line": 385, "column": null}}, "229": {"start": {"line": 386, "column": 6}, "end": {"line": 386, "column": null}}, "230": {"start": {"line": 387, "column": 6}, "end": {"line": 387, "column": null}}, "231": {"start": {"line": 388, "column": 6}, "end": {"line": 388, "column": null}}, "232": {"start": {"line": 389, "column": 6}, "end": {"line": 389, "column": null}}, "233": {"start": {"line": 407, "column": 2}, "end": {"line": 415, "column": null}}, "234": {"start": {"line": 413, "column": 18}, "end": {"line": 413, "column": 50}}}, "fnMap": {"0": {"name": "useMouseTracking", "decl": {"start": {"line": 27, "column": 9}, "end": {"line": 27, "column": null}}, "loc": {"start": {"line": 27, "column": 9}, "end": {"line": 44, "column": null}}}, "1": {"name": "(anonymous_10)", "decl": {"start": {"line": 31, "column": 12}, "end": {"line": 31, "column": null}}, "loc": {"start": {"line": 31, "column": 12}, "end": {"line": 41, "column": 5}}}, "2": {"name": "(anonymous_11)", "decl": {"start": {"line": 32, "column": 19}, "end": {"line": 32, "column": 20}}, "loc": {"start": {"line": 32, "column": 20}, "end": {"line": 35, "column": null}}}, "3": {"name": "(anonymous_12)", "decl": {"start": {"line": 38, "column": 11}, "end": {"line": 38, "column": null}}, "loc": {"start": {"line": 38, "column": 11}, "end": {"line": 40, "column": null}}}, "4": {"name": "SpotlightProvider", "decl": {"start": {"line": 46, "column": 16}, "end": {"line": 46, "column": 33}}, "loc": {"start": {"line": 46, "column": 77}, "end": {"line": 49, "column": null}}}, "5": {"name": "useSpotlightContext", "decl": {"start": {"line": 51, "column": 16}, "end": {"line": 51, "column": 35}}, "loc": {"start": {"line": 51, "column": 16}, "end": {"line": 55, "column": null}}}, "6": {"name": "useLayoutCalculation", "decl": {"start": {"line": 58, "column": 16}, "end": {"line": 58, "column": 36}}, "loc": {"start": {"line": 62, "column": 1}, "end": {"line": 240, "column": null}}}, "7": {"name": "(anonymous_16)", "decl": {"start": {"line": 69, "column": 22}, "end": {"line": 69, "column": null}}, "loc": {"start": {"line": 69, "column": 22}, "end": {"line": 73, "column": null}}}, "8": {"name": "(anonymous_17)", "decl": {"start": {"line": 75, "column": 25}, "end": {"line": 75, "column": null}}, "loc": {"start": {"line": 75, "column": 25}, "end": {"line": 81, "column": null}}}, "9": {"name": "(anonymous_18)", "decl": {"start": {"line": 77, "column": 38}, "end": {"line": 77, "column": null}}, "loc": {"start": {"line": 77, "column": 38}, "end": {"line": 80, "column": null}}}, "10": {"name": "(anonymous_19)", "decl": {"start": {"line": 83, "column": 24}, "end": {"line": 83, "column": null}}, "loc": {"start": {"line": 83, "column": 24}, "end": {"line": 211, "column": null}}}, "11": {"name": "(anonymous_20)", "decl": {"start": {"line": 95, "column": 20}, "end": {"line": 95, "column": 21}}, "loc": {"start": {"line": 95, "column": 21}, "end": {"line": 98, "column": null}}}, "12": {"name": "(anonymous_21)", "decl": {"start": {"line": 102, "column": 11}, "end": {"line": 102, "column": 17}}, "loc": {"start": {"line": 102, "column": 17}, "end": {"line": 107, "column": null}}}, "13": {"name": "(anonymous_22)", "decl": {"start": {"line": 108, "column": 14}, "end": {"line": 108, "column": 19}}, "loc": {"start": {"line": 108, "column": 19}, "end": {"line": 108, "column": 55}}}, "14": {"name": "(anonymous_23)", "decl": {"start": {"line": 111, "column": 42}, "end": {"line": 111, "column": 49}}, "loc": {"start": {"line": 111, "column": 49}, "end": {"line": 117, "column": null}}}, "15": {"name": "(anonymous_24)", "decl": {"start": {"line": 124, "column": 63}, "end": {"line": 124, "column": null}}, "loc": {"start": {"line": 125, "column": 6}, "end": {"line": 125, "column": null}}}, "16": {"name": "(anonymous_25)", "decl": {"start": {"line": 139, "column": 21}, "end": {"line": 139, "column": 22}}, "loc": {"start": {"line": 139, "column": 22}, "end": {"line": 196, "column": null}}}, "17": {"name": "(anonymous_26)", "decl": {"start": {"line": 172, "column": 58}, "end": {"line": 172, "column": null}}, "loc": {"start": {"line": 172, "column": 58}, "end": {"line": 185, "column": null}}}, "18": {"name": "(anonymous_27)", "decl": {"start": {"line": 213, "column": 31}, "end": {"line": 213, "column": 32}}, "loc": {"start": {"line": 213, "column": 53}, "end": {"line": 222, "column": null}}}, "19": {"name": "(anonymous_28)", "decl": {"start": {"line": 224, "column": 22}, "end": {"line": 224, "column": null}}, "loc": {"start": {"line": 224, "column": 22}, "end": {"line": 232, "column": null}}}, "20": {"name": "(anonymous_29)", "decl": {"start": {"line": 227, "column": 34}, "end": {"line": 227, "column": null}}, "loc": {"start": {"line": 227, "column": 34}, "end": {"line": 229, "column": null}}}, "21": {"name": "(anonymous_30)", "decl": {"start": {"line": 234, "column": 29}, "end": {"line": 234, "column": null}}, "loc": {"start": {"line": 234, "column": 29}, "end": {"line": 237, "column": null}}}, "22": {"name": "useSpotlight", "decl": {"start": {"line": 261, "column": 16}, "end": {"line": 261, "column": 28}}, "loc": {"start": {"line": 268, "column": 1}, "end": {"line": 416, "column": null}}}, "23": {"name": "(anonymous_32)", "decl": {"start": {"line": 287, "column": 12}, "end": {"line": 287, "column": null}}, "loc": {"start": {"line": 287, "column": 12}, "end": {"line": 391, "column": 5}}}, "24": {"name": "(anonymous_33)", "decl": {"start": {"line": 296, "column": 19}, "end": {"line": 296, "column": null}}, "loc": {"start": {"line": 296, "column": 19}, "end": {"line": 345, "column": null}}}, "25": {"name": "(anonymous_34)", "decl": {"start": {"line": 347, "column": 26}, "end": {"line": 347, "column": null}}, "loc": {"start": {"line": 347, "column": 26}, "end": {"line": 352, "column": null}}}, "26": {"name": "(anonymous_35)", "decl": {"start": {"line": 354, "column": 23}, "end": {"line": 354, "column": null}}, "loc": {"start": {"line": 354, "column": 23}, "end": {"line": 361, "column": null}}}, "27": {"name": "(anonymous_36)", "decl": {"start": {"line": 355, "column": 28}, "end": {"line": 355, "column": null}}, "loc": {"start": {"line": 355, "column": 28}, "end": {"line": 360, "column": null}}}, "28": {"name": "(anonymous_37)", "decl": {"start": {"line": 356, "column": 30}, "end": {"line": 356, "column": null}}, "loc": {"start": {"line": 356, "column": 30}, "end": {"line": 359, "column": null}}}, "29": {"name": "(anonymous_38)", "decl": {"start": {"line": 367, "column": 21}, "end": {"line": 367, "column": null}}, "loc": {"start": {"line": 367, "column": 21}, "end": {"line": 370, "column": null}}}, "30": {"name": "(anonymous_39)", "decl": {"start": {"line": 371, "column": 21}, "end": {"line": 371, "column": null}}, "loc": {"start": {"line": 371, "column": 21}, "end": {"line": 374, "column": null}}}, "31": {"name": "(anonymous_40)", "decl": {"start": {"line": 380, "column": 19}, "end": {"line": 380, "column": 25}}, "loc": {"start": {"line": 380, "column": 25}, "end": {"line": 380, "column": null}}}, "32": {"name": "(anonymous_41)", "decl": {"start": {"line": 383, "column": 11}, "end": {"line": 383, "column": null}}, "loc": {"start": {"line": 383, "column": 11}, "end": {"line": 390, "column": null}}}, "33": {"name": "(anonymous_42)", "decl": {"start": {"line": 413, "column": 6}, "end": {"line": 413, "column": 7}}, "loc": {"start": {"line": 413, "column": 18}, "end": {"line": 413, "column": 50}}}}, "branchMap": {"0": {"loc": {"start": {"line": 53, "column": 2}, "end": {"line": 53, "column": null}}, "type": "if", "locations": [{"start": {"line": 53, "column": 2}, "end": {"line": 53, "column": null}}]}, "1": {"loc": {"start": {"line": 63, "column": 29}, "end": {"line": 63, "column": 66}}, "type": "default-arg", "locations": [{"start": {"line": 63, "column": 48}, "end": {"line": 63, "column": 66}}]}, "2": {"loc": {"start": {"line": 71, "column": 4}, "end": {"line": 71, "column": null}}, "type": "if", "locations": [{"start": {"line": 71, "column": 4}, "end": {"line": 71, "column": null}}]}, "3": {"loc": {"start": {"line": 85, "column": 21}, "end": {"line": 85, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 85, "column": 21}, "end": {"line": 85, "column": 54}}, {"start": {"line": 85, "column": 54}, "end": {"line": 85, "column": null}}]}, "4": {"loc": {"start": {"line": 86, "column": 4}, "end": {"line": 86, "column": null}}, "type": "if", "locations": [{"start": {"line": 86, "column": 4}, "end": {"line": 86, "column": null}}]}, "5": {"loc": {"start": {"line": 86, "column": 8}, "end": {"line": 86, "column": 28}}, "type": "binary-expr", "locations": [{"start": {"line": 86, "column": 8}, "end": {"line": 86, "column": 17}}, {"start": {"line": 86, "column": 17}, "end": {"line": 86, "column": 28}}]}, "6": {"loc": {"start": {"line": 96, "column": 6}, "end": {"line": 96, "column": null}}, "type": "if", "locations": [{"start": {"line": 96, "column": 6}, "end": {"line": 96, "column": null}}]}, "7": {"loc": {"start": {"line": 99, "column": 40}, "end": {"line": 99, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 99, "column": 40}, "end": {"line": 99, "column": 80}}, {"start": {"line": 99, "column": 80}, "end": {"line": 99, "column": null}}]}, "8": {"loc": {"start": {"line": 108, "column": 19}, "end": {"line": 108, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 108, "column": 19}, "end": {"line": 108, "column": 35}}, {"start": {"line": 108, "column": 39}, "end": {"line": 108, "column": 55}}]}, "9": {"loc": {"start": {"line": 113, "column": 9}, "end": {"line": 113, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 113, "column": 9}, "end": {"line": 113, "column": 23}}, {"start": {"line": 113, "column": 27}, "end": {"line": 113, "column": null}}]}, "10": {"loc": {"start": {"line": 114, "column": 9}, "end": {"line": 114, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 114, "column": 9}, "end": {"line": 114, "column": 24}}, {"start": {"line": 114, "column": 28}, "end": {"line": 114, "column": null}}]}, "11": {"loc": {"start": {"line": 134, "column": 6}, "end": {"line": 136, "column": null}}, "type": "if", "locations": [{"start": {"line": 134, "column": 6}, "end": {"line": 136, "column": null}}]}, "12": {"loc": {"start": {"line": 134, "column": 10}, "end": {"line": 134, "column": 36}}, "type": "binary-expr", "locations": [{"start": {"line": 134, "column": 10}, "end": {"line": 134, "column": 24}}, {"start": {"line": 134, "column": 24}, "end": {"line": 134, "column": 36}}]}, "13": {"loc": {"start": {"line": 147, "column": 8}, "end": {"line": 149, "column": null}}, "type": "if", "locations": [{"start": {"line": 147, "column": 8}, "end": {"line": 149, "column": null}}]}, "14": {"loc": {"start": {"line": 147, "column": 12}, "end": {"line": 147, "column": 30}}, "type": "binary-expr", "locations": [{"start": {"line": 147, "column": 12}, "end": {"line": 147, "column": 22}}, {"start": {"line": 147, "column": 22}, "end": {"line": 147, "column": 30}}]}, "15": {"loc": {"start": {"line": 152, "column": 8}, "end": {"line": 152, "column": null}}, "type": "if", "locations": [{"start": {"line": 152, "column": 8}, "end": {"line": 152, "column": null}}]}, "16": {"loc": {"start": {"line": 152, "column": 12}, "end": {"line": 152, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 152, "column": 12}, "end": {"line": 152, "column": 32}}, {"start": {"line": 152, "column": 32}, "end": {"line": 152, "column": 50}}]}, "17": {"loc": {"start": {"line": 154, "column": 25}, "end": {"line": 154, "column": 47}}, "type": "binary-expr", "locations": [{"start": {"line": 154, "column": 25}, "end": {"line": 154, "column": 43}}, {"start": {"line": 154, "column": 43}, "end": {"line": 154, "column": 47}}]}, "18": {"loc": {"start": {"line": 156, "column": 12}, "end": {"line": 159, "column": null}}, "type": "if", "locations": [{"start": {"line": 156, "column": 12}, "end": {"line": 159, "column": null}}]}, "19": {"loc": {"start": {"line": 160, "column": 8}, "end": {"line": 160, "column": null}}, "type": "if", "locations": [{"start": {"line": 160, "column": 8}, "end": {"line": 160, "column": null}}]}, "20": {"loc": {"start": {"line": 167, "column": 20}, "end": {"line": 167, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 167, "column": 31}, "end": {"line": 167, "column": 54}}, {"start": {"line": 167, "column": 54}, "end": {"line": 167, "column": null}}]}, "21": {"loc": {"start": {"line": 168, "column": 20}, "end": {"line": 168, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 168, "column": 31}, "end": {"line": 168, "column": 54}}, {"start": {"line": 168, "column": 54}, "end": {"line": 168, "column": null}}]}, "22": {"loc": {"start": {"line": 180, "column": 12}, "end": {"line": 183, "column": 42}}, "type": "binary-expr", "locations": [{"start": {"line": 180, "column": 12}, "end": {"line": 180, "column": 43}}, {"start": {"line": 181, "column": 12}, "end": {"line": 181, "column": 43}}, {"start": {"line": 182, "column": 12}, "end": {"line": 182, "column": 43}}, {"start": {"line": 183, "column": 12}, "end": {"line": 183, "column": 42}}]}, "23": {"loc": {"start": {"line": 186, "column": 8}, "end": {"line": 186, "column": null}}, "type": "if", "locations": [{"start": {"line": 186, "column": 8}, "end": {"line": 186, "column": null}}]}, "24": {"loc": {"start": {"line": 191, "column": 12}, "end": {"line": 191, "column": null}}, "type": "if", "locations": [{"start": {"line": 191, "column": 12}, "end": {"line": 191, "column": null}}]}, "25": {"loc": {"start": {"line": 199, "column": 6}, "end": {"line": 203, "column": null}}, "type": "if", "locations": [{"start": {"line": 199, "column": 6}, "end": {"line": 203, "column": null}}]}, "26": {"loc": {"start": {"line": 213, "column": 32}, "end": {"line": 213, "column": 53}}, "type": "default-arg", "locations": [{"start": {"line": 213, "column": 48}, "end": {"line": 213, "column": 53}}]}, "27": {"loc": {"start": {"line": 215, "column": 4}, "end": {"line": 215, "column": null}}, "type": "if", "locations": [{"start": {"line": 215, "column": 4}, "end": {"line": 215, "column": null}}]}, "28": {"loc": {"start": {"line": 217, "column": 4}, "end": {"line": 220, "column": null}}, "type": "if", "locations": [{"start": {"line": 217, "column": 4}, "end": {"line": 220, "column": null}}]}, "29": {"loc": {"start": {"line": 217, "column": 8}, "end": {"line": 217, "column": 47}}, "type": "binary-expr", "locations": [{"start": {"line": 217, "column": 8}, "end": {"line": 217, "column": 25}}, {"start": {"line": 217, "column": 25}, "end": {"line": 217, "column": 47}}]}, "30": {"loc": {"start": {"line": 226, "column": 4}, "end": {"line": 226, "column": null}}, "type": "if", "locations": [{"start": {"line": 226, "column": 4}, "end": {"line": 226, "column": null}}]}, "31": {"loc": {"start": {"line": 226, "column": 8}, "end": {"line": 226, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 226, "column": 8}, "end": {"line": 226, "column": 17}}, {"start": {"line": 226, "column": 17}, "end": {"line": 226, "column": 56}}]}, "32": {"loc": {"start": {"line": 273, "column": 4}, "end": {"line": 273, "column": 47}}, "type": "default-arg", "locations": [{"start": {"line": 273, "column": 18}, "end": {"line": 273, "column": 47}}]}, "33": {"loc": {"start": {"line": 274, "column": 4}, "end": {"line": 274, "column": 47}}, "type": "default-arg", "locations": [{"start": {"line": 274, "column": 18}, "end": {"line": 274, "column": 47}}]}, "34": {"loc": {"start": {"line": 275, "column": 4}, "end": {"line": 275, "column": 44}}, "type": "default-arg", "locations": [{"start": {"line": 275, "column": 13}, "end": {"line": 275, "column": 44}}]}, "35": {"loc": {"start": {"line": 290, "column": 4}, "end": {"line": 290, "column": null}}, "type": "if", "locations": [{"start": {"line": 290, "column": 4}, "end": {"line": 290, "column": null}}]}, "36": {"loc": {"start": {"line": 290, "column": 8}, "end": {"line": 290, "column": 24}}, "type": "binary-expr", "locations": [{"start": {"line": 290, "column": 8}, "end": {"line": 290, "column": 17}}, {"start": {"line": 290, "column": 17}, "end": {"line": 290, "column": 24}}]}, "37": {"loc": {"start": {"line": 299, "column": 6}, "end": {"line": 299, "column": null}}, "type": "if", "locations": [{"start": {"line": 299, "column": 6}, "end": {"line": 299, "column": null}}]}, "38": {"loc": {"start": {"line": 304, "column": 8}, "end": {"line": 307, "column": 33}}, "type": "binary-expr", "locations": [{"start": {"line": 304, "column": 8}, "end": {"line": 304, "column": 31}}, {"start": {"line": 305, "column": 8}, "end": {"line": 305, "column": 32}}, {"start": {"line": 306, "column": 8}, "end": {"line": 306, "column": 30}}, {"start": {"line": 307, "column": 8}, "end": {"line": 307, "column": 33}}]}, "39": {"loc": {"start": {"line": 309, "column": 6}, "end": {"line": 319, "column": null}}, "type": "if", "locations": [{"start": {"line": 309, "column": 6}, "end": {"line": 319, "column": null}}]}, "40": {"loc": {"start": {"line": 333, "column": 8}, "end": {"line": 338, "column": null}}, "type": "if", "locations": [{"start": {"line": 333, "column": 8}, "end": {"line": 338, "column": null}}, {"start": {"line": 334, "column": 13}, "end": {"line": 338, "column": null}}]}, "41": {"loc": {"start": {"line": 334, "column": 13}, "end": {"line": 338, "column": null}}, "type": "if", "locations": [{"start": {"line": 334, "column": 13}, "end": {"line": 338, "column": null}}, {"start": {"line": 335, "column": 13}, "end": {"line": 338, "column": null}}]}, "42": {"loc": {"start": {"line": 348, "column": 6}, "end": {"line": 351, "column": null}}, "type": "if", "locations": [{"start": {"line": 348, "column": 6}, "end": {"line": 351, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0, "108": 0, "109": 0, "110": 0, "111": 0, "112": 0, "113": 0, "114": 0, "115": 0, "116": 0, "117": 0, "118": 0, "119": 0, "120": 0, "121": 0, "122": 0, "123": 0, "124": 0, "125": 0, "126": 0, "127": 0, "128": 0, "129": 0, "130": 0, "131": 0, "132": 0, "133": 0, "134": 0, "135": 0, "136": 0, "137": 0, "138": 0, "139": 0, "140": 0, "141": 0, "142": 0, "143": 0, "144": 0, "145": 0, "146": 0, "147": 0, "148": 0, "149": 0, "150": 0, "151": 0, "152": 0, "153": 0, "154": 0, "155": 0, "156": 0, "157": 0, "158": 0, "159": 0, "160": 0, "161": 0, "162": 0, "163": 0, "164": 0, "165": 0, "166": 0, "167": 0, "168": 0, "169": 0, "170": 0, "171": 0, "172": 0, "173": 0, "174": 0, "175": 0, "176": 0, "177": 0, "178": 0, "179": 0, "180": 0, "181": 0, "182": 0, "183": 0, "184": 0, "185": 0, "186": 0, "187": 0, "188": 0, "189": 0, "190": 0, "191": 0, "192": 0, "193": 0, "194": 0, "195": 0, "196": 0, "197": 0, "198": 0, "199": 0, "200": 0, "201": 0, "202": 0, "203": 0, "204": 0, "205": 0, "206": 0, "207": 0, "208": 0, "209": 0, "210": 0, "211": 0, "212": 0, "213": 0, "214": 0, "215": 0, "216": 0, "217": 0, "218": 0, "219": 0, "220": 0, "221": 0, "222": 0, "223": 0, "224": 0, "225": 0, "226": 0, "227": 0, "228": 0, "229": 0, "230": 0, "231": 0, "232": 0, "233": 0, "234": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0, 0], "4": [0], "5": [0, 0], "6": [0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0], "12": [0, 0], "13": [0], "14": [0, 0], "15": [0], "16": [0, 0], "17": [0, 0], "18": [0], "19": [0], "20": [0, 0], "21": [0, 0], "22": [0, 0, 0, 0], "23": [0], "24": [0], "25": [0], "26": [0], "27": [0], "28": [0], "29": [0, 0], "30": [0], "31": [0, 0], "32": [0], "33": [0], "34": [0], "35": [0], "36": [0, 0], "37": [0], "38": [0, 0, 0, 0], "39": [0], "40": [0, 0], "41": [0, 0], "42": [0]}}, "/Users/<USER>/Downloads/Code/go42/lib/constants.ts": {"path": "/Users/<USER>/Downloads/Code/go42/lib/constants.ts", "statementMap": {"0": {"start": {"line": 29, "column": 13}, "end": {"line": 29, "column": 26}}, "1": {"start": {"line": 7, "column": 13}, "end": {"line": 7, "column": 29}}, "2": {"start": {"line": 41, "column": 13}, "end": {"line": 41, "column": 27}}, "3": {"start": {"line": 19, "column": 13}, "end": {"line": 19, "column": 29}}, "4": {"start": {"line": 7, "column": 32}, "end": {"line": 16, "column": null}}, "5": {"start": {"line": 19, "column": 32}, "end": {"line": 26, "column": null}}, "6": {"start": {"line": 29, "column": 29}, "end": {"line": 38, "column": null}}, "7": {"start": {"line": 41, "column": 30}, "end": {"line": 44, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 6201, "1": 0, "2": 0, "3": 0, "4": 1, "5": 1, "6": 1, "7": 1}, "f": {}, "b": {}}, "/Users/<USER>/Downloads/Code/go42/lib/error-logger.ts": {"path": "/Users/<USER>/Downloads/Code/go42/lib/error-logger.ts", "statementMap": {"0": {"start": {"line": 171, "column": 13}, "end": {"line": 171, "column": 24}}, "1": {"start": {"line": 152, "column": 13}, "end": {"line": 152, "column": 26}}, "2": {"start": {"line": 131, "column": 13}, "end": {"line": 131, "column": 30}}, "3": {"start": {"line": 33, "column": 13}, "end": {"line": 33, "column": 21}}, "4": {"start": {"line": 109, "column": 13}, "end": {"line": 109, "column": 34}}, "5": {"start": {"line": 33, "column": 24}, "end": {"line": 100, "column": null}}, "6": {"start": {"line": 43, "column": 6}, "end": {"line": 43, "column": null}}, "7": {"start": {"line": 46, "column": 20}, "end": {"line": 62, "column": null}}, "8": {"start": {"line": 65, "column": 19}, "end": {"line": 65, "column": null}}, "9": {"start": {"line": 66, "column": 2}, "end": {"line": 66, "column": null}}, "10": {"start": {"line": 69, "column": 2}, "end": {"line": 99, "column": null}}, "11": {"start": {"line": 83, "column": 4}, "end": {"line": 98, "column": null}}, "12": {"start": {"line": 84, "column": 6}, "end": {"line": 95, "column": null}}, "13": {"start": {"line": 85, "column": 8}, "end": {"line": 94, "column": null}}, "14": {"start": {"line": 109, "column": 37}, "end": {"line": 122, "column": null}}, "15": {"start": {"line": 114, "column": 2}, "end": {"line": 121, "column": null}}, "16": {"start": {"line": 131, "column": 33}, "end": {"line": 143, "column": null}}, "17": {"start": {"line": 136, "column": 2}, "end": {"line": 142, "column": null}}, "18": {"start": {"line": 152, "column": 29}, "end": {"line": 162, "column": null}}, "19": {"start": {"line": 157, "column": 2}, "end": {"line": 161, "column": null}}, "20": {"start": {"line": 171, "column": 27}, "end": {"line": 184, "column": null}}, "21": {"start": {"line": 176, "column": 2}, "end": {"line": 183, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_6)", "decl": {"start": {"line": 33, "column": 24}, "end": {"line": 33, "column": null}}, "loc": {"start": {"line": 36, "column": 31}, "end": {"line": 100, "column": null}}}, "1": {"name": "(anonymous_7)", "decl": {"start": {"line": 91, "column": 17}, "end": {"line": 91, "column": null}}, "loc": {"start": {"line": 91, "column": 17}, "end": {"line": 94, "column": null}}}, "2": {"name": "(anonymous_8)", "decl": {"start": {"line": 109, "column": 37}, "end": {"line": 109, "column": null}}, "loc": {"start": {"line": 112, "column": 2}, "end": {"line": 122, "column": null}}}, "3": {"name": "(anonymous_9)", "decl": {"start": {"line": 131, "column": 33}, "end": {"line": 131, "column": null}}, "loc": {"start": {"line": 134, "column": 2}, "end": {"line": 143, "column": null}}}, "4": {"name": "(anonymous_10)", "decl": {"start": {"line": 152, "column": 29}, "end": {"line": 152, "column": null}}, "loc": {"start": {"line": 155, "column": 2}, "end": {"line": 162, "column": null}}}, "5": {"name": "(anonymous_11)", "decl": {"start": {"line": 171, "column": 27}, "end": {"line": 171, "column": null}}, "loc": {"start": {"line": 174, "column": 2}, "end": {"line": 184, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 36, "column": 2}, "end": {"line": 36, "column": 31}}, "type": "default-arg", "locations": [{"start": {"line": 36, "column": 29}, "end": {"line": 36, "column": 31}}]}, "1": {"loc": {"start": {"line": 39, "column": 4}, "end": {"line": 39, "column": 24}}, "type": "default-arg", "locations": [{"start": {"line": 39, "column": 15}, "end": {"line": 39, "column": 24}}]}, "2": {"loc": {"start": {"line": 40, "column": 4}, "end": {"line": 40, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 40, "column": 14}, "end": {"line": 40, "column": 16}}]}, "3": {"loc": {"start": {"line": 41, "column": 4}, "end": {"line": 41, "column": 13}}, "type": "default-arg", "locations": [{"start": {"line": 41, "column": 11}, "end": {"line": 41, "column": 13}}]}, "4": {"loc": {"start": {"line": 42, "column": 4}, "end": {"line": 42, "column": 14}}, "type": "default-arg", "locations": [{"start": {"line": 42, "column": 12}, "end": {"line": 42, "column": 14}}]}, "5": {"loc": {"start": {"line": 57, "column": 11}, "end": {"line": 57, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 57, "column": 43}, "end": {"line": 57, "column": 63}}, {"start": {"line": 57, "column": 66}, "end": {"line": 57, "column": null}}]}, "6": {"loc": {"start": {"line": 58, "column": 17}, "end": {"line": 58, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 58, "column": 49}, "end": {"line": 58, "column": 75}}, {"start": {"line": 58, "column": 78}, "end": {"line": 58, "column": null}}]}, "7": {"loc": {"start": {"line": 65, "column": 19}, "end": {"line": 65, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 65, "column": 45}, "end": {"line": 65, "column": 55}}, {"start": {"line": 65, "column": 55}, "end": {"line": 65, "column": null}}]}, "8": {"loc": {"start": {"line": 65, "column": 55}, "end": {"line": 65, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 65, "column": 80}, "end": {"line": 65, "column": 89}}, {"start": {"line": 65, "column": 89}, "end": {"line": 65, "column": null}}]}, "9": {"loc": {"start": {"line": 69, "column": 2}, "end": {"line": 99, "column": null}}, "type": "if", "locations": [{"start": {"line": 69, "column": 2}, "end": {"line": 99, "column": null}}]}, "10": {"loc": {"start": {"line": 84, "column": 6}, "end": {"line": 95, "column": null}}, "type": "if", "locations": [{"start": {"line": 84, "column": 6}, "end": {"line": 95, "column": null}}]}, "11": {"loc": {"start": {"line": 84, "column": 10}, "end": {"line": 84, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 84, "column": 10}, "end": {"line": 84, "column": 43}}, {"start": {"line": 84, "column": 43}, "end": {"line": 84, "column": 55}}]}, "12": {"loc": {"start": {"line": 136, "column": 46}, "end": {"line": 136, "column": 85}}, "type": "cond-expr", "locations": [{"start": {"line": 136, "column": 60}, "end": {"line": 136, "column": 78}}, {"start": {"line": 136, "column": 81}, "end": {"line": 136, "column": 85}}]}}, "s": {"0": 2, "1": 2, "2": 2, "3": 10, "4": 1, "5": 1, "6": 17, "7": 17, "8": 17, "9": 17, "10": 17, "11": 3, "12": 3, "13": 2, "14": 1, "15": 1, "16": 1, "17": 2, "18": 1, "19": 2, "20": 1, "21": 2}, "f": {"0": 17, "1": 0, "2": 1, "3": 2, "4": 2, "5": 2}, "b": {"0": [7], "1": [7], "2": [15], "3": [9], "4": [11], "5": [16, 1], "6": [16, 1], "7": [2, 15], "8": [14, 1], "9": [3], "10": [2], "11": [3, 3], "12": [1, 1]}}, "/Users/<USER>/Downloads/Code/go42/lib/utils.ts": {"path": "/Users/<USER>/Downloads/Code/go42/lib/utils.ts", "statementMap": {"0": {"start": {"line": 4, "column": 16}, "end": {"line": 4, "column": 19}}, "1": {"start": {"line": 1, "column": 38}, "end": {"line": 1, "column": null}}, "2": {"start": {"line": 2, "column": 24}, "end": {"line": 2, "column": null}}, "3": {"start": {"line": 5, "column": 2}, "end": {"line": 5, "column": null}}}, "fnMap": {"0": {"name": "cn", "decl": {"start": {"line": 4, "column": 16}, "end": {"line": 4, "column": 19}}, "loc": {"start": {"line": 4, "column": 42}, "end": {"line": 6, "column": null}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {"0": 0}, "b": {}}}