# Testing Guide for Go42 Project

This document provides comprehensive information about the testing setup and test suites for the Go42 project.

## Overview

The project uses Jest and React Testing Library for testing, with a focus on:
- **Unit Tests**: Testing individual functions and utilities
- **Component Tests**: Testing React components with accessibility focus
- **Integration Tests**: Testing component interactions and data flow

## Test Setup

### Dependencies

The following testing dependencies are included:

```json
{
  "@testing-library/jest-dom": "^6.1.4",
  "@testing-library/react": "^14.1.2", 
  "@testing-library/user-event": "^14.5.1",
  "@types/jest": "^29.5.8",
  "jest": "^29.7.0",
  "jest-environment-jsdom": "^29.7.0",
  "ts-jest": "^29.1.1"
}
```

### Configuration

- **Jest Config**: `jest.config.js` - Main Jest configuration with Next.js integration
- **Setup File**: `jest.setup.js` - Global test setup and mocks
- **TypeScript**: Full TypeScript support with `ts-jest`

## Running Tests

### Available Scripts

```bash
# Run all tests once
npm run test

# Run tests in watch mode (development)
npm run test:watch

# Run tests with coverage report
npm run test:coverage

# Run tests for CI/CD (no watch, with coverage)
npm run test:ci

# Run all checks (type-check, lint, format, test)
npm run check-all
```

### Coverage Thresholds

The project maintains high code coverage standards:
- **Branches**: 80%
- **Functions**: 80%
- **Lines**: 80%
- **Statements**: 80%

## Test Suites

### 1. Error Logger Utility Tests

**Location**: `lib/__tests__/error-logger.test.ts`

**Coverage**:
- ✅ All logging functions (`logError`, `logErrorBoundaryError`, `logComponentError`, `logAsyncError`, `logApiError`)
- ✅ Different severity levels (critical, warning, info)
- ✅ Custom context, tags, and extra data
- ✅ Server-side rendering compatibility
- ✅ Production error tracking integration
- ✅ Error handling edge cases

**Key Test Scenarios**:
```typescript
// Basic error logging
logError('Test message', new Error('Test error'))

// Error boundary logging
logErrorBoundaryError('ComponentName', error, errorInfo)

// Production error tracking
process.env.NODE_ENV = 'production'
// Tests fetch calls to error tracking service
```

### 2. TypingTerminal Accessibility Tests

**Location**: `components/ui/__tests__/TypingTerminal.test.tsx`

**Coverage**:
- ✅ Screen reader support (ARIA attributes, live regions)
- ✅ Reduced motion preferences (`prefers-reduced-motion`)
- ✅ Keyboard navigation and focus management
- ✅ Loading states and transitions
- ✅ Dynamic content announcements
- ✅ Edge cases and error handling

**Key Test Scenarios**:
```typescript
// Screen reader support
expect(screen.getByRole('status')).toHaveAttribute('aria-live', 'polite')

// Reduced motion
Object.defineProperty(window, 'matchMedia', {
  value: () => ({ matches: true }) // prefers-reduced-motion: reduce
})

// Dynamic announcements
await waitFor(() => {
  expect(statusRegion.textContent).toMatch(/Typing:/)
})
```

### 3. Spotlight Animation Constants Integration Tests

**Location**: `hooks/__tests__/spotlight-constants.test.ts`

**Coverage**:
- ✅ Constants validation and immutability
- ✅ Grid calculation logic with various container sizes
- ✅ Minimum value constraints
- ✅ Cell dimension calculations
- ✅ Responsive design breakpoints
- ✅ Performance considerations
- ✅ Backward compatibility with original hardcoded values

**Key Test Scenarios**:
```typescript
// Grid calculations
const cols = Math.max(LAYOUT_CONFIG.MIN_COLS, Math.round(width / LAYOUT_CONFIG.CELL_WIDTH_DIVISOR))
const rows = Math.max(LAYOUT_CONFIG.MIN_ROWS, Math.round(height / LAYOUT_CONFIG.CELL_HEIGHT))

// Responsive breakpoints
const breakpoints = [
  { name: 'mobile', width: 375, height: 667 },
  { name: 'desktop', width: 1920, height: 1080 }
]
```

## Testing Best Practices

### 1. Accessibility Testing

All component tests include accessibility checks:

```typescript
// ARIA attributes
expect(element).toHaveAttribute('aria-label', 'Expected label')
expect(element).toHaveAttribute('role', 'status')

// Screen reader content
expect(screen.getByRole('status')).toHaveTextContent('Expected announcement')

// Reduced motion
const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches
```

### 2. Error Handling

Tests cover error scenarios and edge cases:

```typescript
// Error boundary testing
const ThrowError = () => { throw new Error('Test error') }
render(<ErrorBoundary><ThrowError /></ErrorBoundary>)

// Async error handling
await expect(asyncFunction()).rejects.toThrow('Expected error')
```

### 3. Mocking

Comprehensive mocking for browser APIs:

```typescript
// Media queries
window.matchMedia = jest.fn().mockImplementation(query => ({
  matches: false,
  addEventListener: jest.fn(),
  removeEventListener: jest.fn()
}))

// Intersection Observer
global.IntersectionObserver = class IntersectionObserver {
  observe() {}
  disconnect() {}
}
```

## Continuous Integration

### GitHub Actions Integration

The test suite is designed for CI/CD integration:

```yaml
# Example GitHub Actions step
- name: Run tests
  run: npm run test:ci

- name: Upload coverage
  uses: codecov/codecov-action@v3
  with:
    file: ./coverage/lcov.info
```

### Pre-commit Hooks

Consider adding pre-commit hooks to run tests:

```json
{
  "husky": {
    "hooks": {
      "pre-commit": "npm run check-all"
    }
  }
}
```

## Writing New Tests

### Test File Structure

```typescript
// Component test template
import { render, screen, waitFor } from '@testing-library/react'
import { ComponentName } from '../ComponentName'

describe('ComponentName', () => {
  beforeEach(() => {
    // Setup
  })

  describe('Feature Group', () => {
    it('should do something specific', () => {
      // Test implementation
    })
  })
})
```

### Naming Conventions

- Test files: `*.test.ts` or `*.test.tsx`
- Test directories: `__tests__/`
- Describe blocks: Feature or functionality groups
- Test cases: Specific behaviors with "should" statements

## Debugging Tests

### Common Issues

1. **Async Operations**: Use `waitFor` for async state changes
2. **DOM Queries**: Use `screen.debug()` to inspect rendered DOM
3. **Mock Issues**: Ensure mocks are properly reset between tests
4. **Timing Issues**: Increase timeouts for slow operations

### Debug Commands

```bash
# Run specific test file
npm test -- error-logger.test.ts

# Run tests with verbose output
npm test -- --verbose

# Run tests in debug mode
node --inspect-brk node_modules/.bin/jest --runInBand
```

## Coverage Reports

Coverage reports are generated in the `coverage/` directory:
- `coverage/lcov-report/index.html` - HTML coverage report
- `coverage/lcov.info` - LCOV format for CI tools
- `coverage/coverage-final.json` - JSON format

## Future Enhancements

### Planned Test Additions

1. **E2E Tests**: Playwright or Cypress integration
2. **Visual Regression**: Screenshot testing
3. **Performance Tests**: Bundle size and runtime performance
4. **API Tests**: Mock API integration testing

### Test Utilities

Consider adding custom test utilities:
- Custom render functions with providers
- Accessibility testing helpers
- Mock data factories
- Test ID conventions

## Resources

- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [Testing Library Best Practices](https://kentcdodds.com/blog/common-mistakes-with-react-testing-library)
- [Accessibility Testing Guide](https://web.dev/accessibility-testing/)
